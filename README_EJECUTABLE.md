# Sistema Híbrido de Evaluación de Autismo - Ejecutable de Escritorio

## 🧠 Descripción

Aplicación de escritorio independiente para evaluación de autismo en niños de 1-16 años que utiliza algoritmos avanzados de machine learning:

- **DBSCAN**: Para detección de casos atípicos
- **GMM**: Para clasificación de grupos de riesgo
- **Random Forest**: Para predicciones robustas
- **Clustering Interactivo**: Para visualización de patrones

## 🔄 Secuencia de Ejecución

El proyecto sigue una secuencia específica que el ejecutable respeta:

1. **`clustering_visualizer.py`** - Sistema de clustering y visualización
2. **`app.py`** - Servidor Flask (importa clustering_visualizer)
3. **`index.html`** - Interfaz web (se conecta al servidor Flask)

## 🚀 Creación del Ejecutable

### Opción 1: Automática (Recomendada)
```bash
# Ejecutar el script automático
crear_ejecutable.bat
```

### Opción 2: Manual
```bash
# 1. Instalar dependencias
python instalar_dependencias.py

# 2. Probar secuencia
python test_secuencia.py

# 3. Crear ejecutable
python build_exe.py
```

### Opción 3: Paso a paso
```bash
# 1. Instalar PyInstaller
pip install pyinstaller

# 2. Instalar dependencias del proyecto
pip install -r requirements.txt

# 3. Crear ejecutable con PyInstaller
pyinstaller --onefile --name=SistemaAutismo --add-data="datos.csv;." --add-data="index.html;." --add-data="styles.css;." --add-data="script.js;." --add-data="clustering_ui.js;." --add-data="PaginaGraficos;PaginaGraficos" --console main.py
```

## 📁 Estructura de Archivos Necesarios

```
austimo/
├── main.py                    # Script principal del ejecutable
├── app.py                     # Aplicación Flask modificada
├── clustering_visualizer.py   # Módulo de clustering
├── datos.csv                  # Datos de entrenamiento
├── index.html                 # Interfaz principal
├── styles.css                 # Estilos CSS
├── script.js                  # JavaScript principal
├── clustering_ui.js           # JavaScript de clustering
├── PaginaGraficos/            # Módulo de gráficos
├── requirements.txt           # Dependencias Python
├── build_exe.py              # Script de construcción
├── instalar_dependencias.py  # Instalador de dependencias
└── crear_ejecutable.bat      # Script automático
```

## 🔧 Requisitos del Sistema

### Para Crear el Ejecutable:
- Python 3.8 o superior
- 2 GB de RAM disponible
- 1 GB de espacio libre
- Conexión a internet (para descargar dependencias)

### Para Ejecutar el .exe:
- Windows 10 o superior
- 4 GB de RAM mínimo
- 500 MB de espacio libre
- Navegador web moderno

## 📋 Uso del Ejecutable

1. **Ejecutar**: Doble clic en `SistemaAutismo.exe`
2. **Esperar**: La aplicación iniciará el servidor interno
3. **Navegar**: Se abrirá automáticamente el navegador web
4. **Evaluar**: Complete el cuestionario de 10 preguntas
5. **Resultados**: Obtenga análisis con visualizaciones interactivas

## ✨ Características del Ejecutable

- ✅ **Independiente**: No requiere Python instalado
- ✅ **Portátil**: Un solo archivo ejecutable
- ✅ **Automático**: Abre el navegador automáticamente
- ✅ **Completo**: Incluye todos los datos y algoritmos
- ✅ **Seguro**: Ejecuta servidor local (127.0.0.1)
- ✅ **Moderno**: Interfaz web responsive

## 🔍 Solución de Problemas

### El ejecutable no inicia:
- Verificar que no hay antivirus bloqueando
- Ejecutar como administrador
- Verificar espacio en disco

### El navegador no se abre:
- Abrir manualmente: `http://127.0.0.1:[puerto]`
- El puerto se muestra en la consola

### Error de dependencias:
- Reinstalar dependencias: `python instalar_dependencias.py`
- Verificar versión de Python

### Error de construcción:
- Verificar que todos los archivos estén presentes
- Ejecutar `python build_exe.py` con permisos de administrador

## 📊 Algoritmos Incluidos

### DBSCAN (Density-Based Spatial Clustering)
- Detecta casos atípicos automáticamente
- No requiere especificar número de clusters
- Maneja ruido en los datos

### GMM (Gaussian Mixture Models)
- Clasifica casos en grupos de riesgo
- Proporciona probabilidades de pertenencia
- Interpretación clínica clara

### Random Forest
- Predicciones robustas y precisas
- Análisis de importancia de características
- Resistente al sobreajuste

### Clustering Interactivo
- Visualización con matplotlib
- Posicionamiento relativo del usuario
- Interpretación de patrones

## 🎯 Grupos de Edad Soportados

- **1-3 años**: Bebés y niños pequeños
- **3-6 años**: Niños preescolares  
- **6-12 años**: Niños escolares
- **12-16 años**: Adolescentes

## ⚠️ Importante

Esta herramienta es para fines informativos y de detección temprana. **NO reemplaza el diagnóstico profesional**. Siempre consulte con un especialista en desarrollo infantil para una evaluación completa.

## 📞 Soporte

Para soporte técnico o preguntas sobre el ejecutable, consulte la documentación incluida o revise los logs de error en la consola del ejecutable.
