#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para instalar todas las dependencias necesarias para el Sistema de Evaluación de Autismo
"""

import subprocess
import sys
import os

def install_package(package):
    """Instala un paquete usando pip"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        return True
    except subprocess.CalledProcessError:
        return False

def main():
    """Función principal para instalar dependencias"""
    print("🧠 INSTALADOR DE DEPENDENCIAS - SISTEMA DE EVALUACIÓN DE AUTISMO")
    print("=" * 60)
    
    # Lista de paquetes necesarios
    packages = [
        "Flask==2.3.3",
        "Flask-CORS==4.0.0",
        "pandas==2.0.3",
        "numpy==1.24.3",
        "scikit-learn==1.3.0",
        "matplotlib==3.7.2",
        "seaborn==0.12.2",
        "pyinstaller==5.13.2",
        "pillow==10.0.0"
    ]
    
    print("📦 Instalando dependencias necesarias...")
    print(f"📋 Total de paquetes: {len(packages)}")
    print()
    
    failed_packages = []
    
    for i, package in enumerate(packages, 1):
        package_name = package.split("==")[0]
        print(f"[{i}/{len(packages)}] Instalando {package_name}...")
        
        if install_package(package):
            print(f"✅ {package_name} instalado correctamente")
        else:
            print(f"❌ Error instalando {package_name}")
            failed_packages.append(package_name)
        print()
    
    print("=" * 60)
    if failed_packages:
        print("❌ INSTALACIÓN COMPLETADA CON ERRORES")
        print(f"📋 Paquetes fallidos: {', '.join(failed_packages)}")
        print("🔧 Intente instalar manualmente los paquetes fallidos")
    else:
        print("✅ TODAS LAS DEPENDENCIAS INSTALADAS CORRECTAMENTE")
        print("🚀 Ya puede ejecutar build_exe.py para crear el ejecutable")
    
    print("=" * 60)
    input("Presione Enter para continuar...")

if __name__ == "__main__":
    main()
