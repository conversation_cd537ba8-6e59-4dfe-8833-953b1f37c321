#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para probar que el puerto 5000 funciona correctamente
"""

import socket
import time
import threading
import webbrowser
from main import AutismAssessmentApp

def test_puerto_5000():
    """Prueba que el sistema use el puerto 5000"""
    print("🔍 PROBANDO PUERTO 5000")
    print("=" * 40)
    
    # Verificar que el puerto 5000 esté disponible
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(('127.0.0.1', 5000))
            print("✅ Puerto 5000 disponible")
            return True
    except OSError:
        print("❌ Puerto 5000 ocupado")
        return False

def test_app_puerto():
    """Prueba que la aplicación use el puerto correcto"""
    print("\n🔍 PROBANDO APLICACIÓN CON PUERTO")
    print("=" * 40)
    
    try:
        app = AutismAssessmentApp()
        puerto = app.find_free_port()
        
        print(f"📍 Puerto asignado: {puerto}")
        
        if puerto == 5000:
            print("✅ Aplicación usa puerto 5000 (correcto)")
            return True
        elif puerto in [5001, 5002, 5003, 5004, 5005]:
            print(f"✅ Aplicación usa puerto alternativo {puerto} (aceptable)")
            return True
        else:
            print(f"⚠️ Aplicación usa puerto aleatorio {puerto}")
            return False
            
    except Exception as e:
        print(f"❌ Error probando aplicación: {e}")
        return False

def test_servidor_completo():
    """Prueba el servidor completo por 30 segundos"""
    print("\n🔍 PROBANDO SERVIDOR COMPLETO")
    print("=" * 40)
    
    try:
        app = AutismAssessmentApp()
        
        # Iniciar servidor en hilo separado
        def run_server():
            try:
                app.run()
            except:
                pass
        
        server_thread = threading.Thread(target=run_server, daemon=True)
        server_thread.start()
        
        # Esperar un poco para que el servidor inicie
        print("⏳ Esperando servidor...")
        time.sleep(5)
        
        # Verificar que el servidor responda
        if hasattr(app, 'port') and app.port:
            print(f"📍 Servidor iniciado en puerto: {app.port}")
            
            # Intentar conexión
            try:
                import urllib.request
                url = f"http://127.0.0.1:{app.port}/health"
                
                request = urllib.request.Request(url)
                with urllib.request.urlopen(request, timeout=5) as response:
                    if response.getcode() == 200:
                        print("✅ Servidor respondiendo correctamente")
                        print(f"🌐 URL: http://127.0.0.1:{app.port}")
                        
                        # Abrir navegador para verificación manual
                        print("🌐 Abriendo navegador para verificación...")
                        webbrowser.open(f"http://127.0.0.1:{app.port}", new=2)
                        
                        print("\n📋 VERIFICACIÓN MANUAL:")
                        print("1. El navegador debería abrirse automáticamente")
                        print("2. Verifique que la página carga correctamente")
                        print("3. En la esquina superior derecha debe mostrar:")
                        print(f"   'Conectado - Puerto {app.port} - Clustering K=2/K=3 Disponible'")
                        print("4. NO debe mostrar 'Sin conexión - Modo local'")
                        
                        # Mantener servidor activo por 30 segundos
                        print(f"\n🔄 Servidor activo por 30 segundos...")
                        for i in range(30):
                            print(f"⏱️ {30-i} segundos restantes...", end='\r')
                            time.sleep(1)
                        
                        print("\n✅ Prueba completada")
                        return True
                    else:
                        print(f"❌ Servidor responde con código: {response.getcode()}")
                        return False
            except Exception as e:
                print(f"❌ Error conectando al servidor: {e}")
                return False
        else:
            print("❌ Servidor no pudo iniciar")
            return False
            
    except Exception as e:
        print(f"❌ Error en prueba completa: {e}")
        return False

def main():
    """Función principal de pruebas"""
    print("🔧 PRUEBA DE PUERTO 5000")
    print("🧠 Sistema de Evaluación de Autismo")
    print("=" * 50)
    
    tests = [
        ("Puerto 5000 Disponible", test_puerto_5000),
        ("Aplicación Puerto", test_app_puerto),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*15} {test_name} {'='*15}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: PASÓ")
            else:
                print(f"❌ {test_name}: FALLÓ")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
    
    print("\n" + "=" * 50)
    print("📊 RESUMEN")
    print("=" * 50)
    print(f"✅ Pruebas básicas: {passed}/{total}")
    
    if passed == total:
        print("🎉 PRUEBAS BÁSICAS EXITOSAS")
        
        respuesta = input("\n¿Ejecutar prueba completa del servidor? (s/N): ")
        if respuesta.lower() == 's':
            print("\n🚀 INICIANDO PRUEBA COMPLETA...")
            if test_servidor_completo():
                print("\n🎉 TODAS LAS PRUEBAS EXITOSAS")
                print("✅ El ejecutable debería funcionar perfectamente")
                print("🔗 Siempre usará puerto 5000 o alternativo cercano")
            else:
                print("\n⚠️ Problemas en prueba completa")
        else:
            print("\n✅ Pruebas básicas completadas")
            print("🚀 El ejecutable debería funcionar correctamente")
    else:
        print("❌ PROBLEMAS EN PRUEBAS BÁSICAS")
        print("🔧 Revise los errores mostrados")
    
    print("=" * 50)
    input("Presione Enter para salir...")

if __name__ == "__main__":
    main()
