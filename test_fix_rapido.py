#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Prueba rápida para verificar que el error del atributo 'data' esté solucionado
"""

import sys
import os

def test_clustering_system():
    """Prueba la inicialización del sistema de clustering"""
    print("🔧 Probando inicialización del sistema de clustering...")
    
    try:
        # Importar el sistema
        from app import clustering_system
        print("✅ Sistema importado correctamente")
        
        # Verificar atributos
        print(f"📊 Tipo de sistema: {type(clustering_system).__name__}")
        print(f"📊 Tiene atributo 'data': {hasattr(clustering_system, 'data')}")
        print(f"📊 Tiene atributo 'dbscan_models': {hasattr(clustering_system, 'dbscan_models')}")
        
        if hasattr(clustering_system, 'data'):
            if clustering_system.data is not None:
                print(f"✅ Datos cargados: {len(clustering_system.data)} casos")
                print(f"📊 Columnas: {list(clustering_system.data.columns)}")
            else:
                print("⚠️ Atributo 'data' es None")
        
        if hasattr(clustering_system, 'dbscan_models'):
            print(f"📊 Modelos DBSCAN: {len(clustering_system.dbscan_models)} configuraciones")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_health_endpoint():
    """Prueba el endpoint de health"""
    print("\n🔧 Probando endpoint de health...")
    
    try:
        from app import app
        
        with app.test_client() as client:
            response = client.get('/health')
            
            if response.status_code == 200:
                print("✅ Health endpoint funcionando")
                data = response.get_json()
                print(f"📊 Status: {data.get('status')}")
                print(f"📊 Data loaded: {data.get('data_loaded')}")
                print(f"📊 Models trained: {data.get('models_trained')}")
                
                if 'system_status' in data:
                    sys_status = data['system_status']
                    print(f"📊 Sistema: {sys_status.get('clustering_system_type')}")
                    print(f"📊 Tiene data: {sys_status.get('has_data_attribute')}")
                    print(f"📊 Data es None: {sys_status.get('data_is_none')}")
                
                return True
            else:
                print(f"❌ Health endpoint error: {response.status_code}")
                print(f"Response: {response.get_data(as_text=True)}")
                return False
                
    except Exception as e:
        print(f"❌ Error en health endpoint: {e}")
        return False

def test_prediction():
    """Prueba una predicción simple"""
    print("\n🔧 Probando predicción...")
    
    try:
        from app import analyze_user_responses
        
        test_data = {
            'A1': 1, 'A2': 1, 'A3': 1, 'A4': 0, 'A5': 1,
            'A6': 1, 'A7': 1, 'A8': 1, 'A9': 0, 'A10': 1,
            'Age_Mons': 36
        }
        
        result = analyze_user_responses(test_data, selected_k=3)
        
        if result and result.get('success'):
            print("✅ Predicción funcionando")
            print(f"📊 Algoritmo: {result.get('selected_algorithm')}")
            print(f"📊 Total casos: {result.get('total_cases')}")
            return True
        else:
            print(f"❌ Error en predicción: {result.get('error') if result else 'Sin resultado'}")
            return False
            
    except Exception as e:
        print(f"❌ Error en predicción: {e}")
        return False

def main():
    """Función principal de pruebas"""
    print("🔧 PRUEBA RÁPIDA - CORRECCIÓN DEL ERROR 'data'")
    print("=" * 50)
    
    tests = [
        ("Sistema de Clustering", test_clustering_system),
        ("Health Endpoint", test_health_endpoint),
        ("Predicción", test_prediction)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        if test_func():
            passed += 1
            print(f"✅ {test_name}: PASÓ")
        else:
            print(f"❌ {test_name}: FALLÓ")
    
    print("\n" + "=" * 50)
    print("📊 RESUMEN")
    print("=" * 50)
    print(f"✅ Pruebas pasadas: {passed}/{total}")
    
    if passed == total:
        print("🎉 TODAS LAS PRUEBAS PASARON")
        print("✅ El error del atributo 'data' está solucionado")
        print("🚀 El ejecutable debería funcionar sin errores en bucle")
    else:
        print("⚠️ ALGUNAS PRUEBAS FALLARON")
        print("🔧 Revise los errores mostrados arriba")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
