#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script específico para probar la secuencia correcta de ejecución:
1. clustering_visualizer.py
2. app.py  
3. index.html
"""

import os
import sys
import time
import threading
import webbrowser
from pathlib import Path

def test_clustering_visualizer():
    """Prueba que clustering_visualizer.py funcione independientemente"""
    print("🔧 PASO 1: Probando clustering_visualizer.py...")
    
    try:
        import clustering_visualizer
        print("✅ clustering_visualizer importado correctamente")
        
        # Probar función principal
        test_data = {
            'A1': 1, 'A2': 1, 'A3': 1, 'A4': 1, 'A5': 1,
            'A6': 1, 'A7': 1, 'A8': 1, 'A9': 1, 'A10': 0,
            'Age_Mons': 36
        }
        
        result = clustering_visualizer.generate_clustering_visualization(test_data, k=3)
        if result and result.get('success'):
            print("✅ clustering_visualizer funcionando correctamente")
            print(f"   • Algoritmo usado: {result.get('selected_algorithm', 'N/A')}")
            return True
        else:
            print("⚠️ clustering_visualizer con advertencias")
            return True  # Continuar aunque haya advertencias
            
    except Exception as e:
        print(f"❌ Error en clustering_visualizer: {e}")
        return False

def test_app_flask():
    """Prueba que app.py funcione después de clustering_visualizer"""
    print("\n🔧 PASO 2: Probando app.py...")
    
    try:
        from app import app, clustering_system
        print("✅ app.py importado correctamente")
        print(f"✅ Sistema de clustering: {type(clustering_system)}")
        
        # Verificar que las rutas estén configuradas
        routes = [rule.rule for rule in app.url_map.iter_rules()]
        print(f"✅ Rutas configuradas: {len(routes)}")
        
        # Verificar rutas importantes
        important_routes = ['/', '/predict', '/health']
        for route in important_routes:
            if route in routes:
                print(f"   ✅ {route}")
            else:
                print(f"   ❌ {route} faltante")
        
        return True
        
    except Exception as e:
        print(f"❌ Error en app.py: {e}")
        return False

def test_index_html():
    """Prueba que index.html sea accesible"""
    print("\n🔧 PASO 3: Probando index.html...")
    
    try:
        if os.path.exists('index.html'):
            with open('index.html', 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"✅ index.html encontrado ({len(content)} caracteres)")
            
            # Verificar elementos importantes
            checks = [
                ('script.js', 'script.js' in content),
                ('styles.css', 'styles.css' in content),
                ('clustering_ui.js', 'clustering_ui.js' in content),
                ('formulario', 'form' in content.lower()),
                ('botón enviar', 'submit' in content.lower() or 'enviar' in content.lower())
            ]
            
            for check_name, check_result in checks:
                if check_result:
                    print(f"   ✅ {check_name}")
                else:
                    print(f"   ⚠️ {check_name} no encontrado")
            
            return True
        else:
            print("❌ index.html no encontrado")
            return False
            
    except Exception as e:
        print(f"❌ Error leyendo index.html: {e}")
        return False

def test_full_sequence():
    """Prueba la secuencia completa simulando el ejecutable"""
    print("\n🔧 PASO 4: Probando secuencia completa...")
    
    try:
        # Simular la secuencia del ejecutable
        print("   [1/4] Importando clustering_visualizer...")
        import clustering_visualizer
        
        print("   [2/4] Importando app...")
        from app import app
        
        print("   [3/4] Configurando Flask para modo de prueba...")
        app.config['TESTING'] = True
        client = app.test_client()
        
        print("   [4/4] Probando ruta principal (/)...")
        response = client.get('/')
        
        if response.status_code == 200:
            print("✅ Ruta principal funcionando")
            print(f"   • Código de estado: {response.status_code}")
            print(f"   • Tipo de contenido: {response.content_type}")
            return True
        else:
            print(f"❌ Error en ruta principal: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error en secuencia completa: {e}")
        return False

def main():
    """Función principal de pruebas de secuencia"""
    print("🧠 PRUEBA DE SECUENCIA DE EJECUCIÓN")
    print("=" * 50)
    print("Verificando: clustering_visualizer.py → app.py → index.html")
    print("=" * 50)
    
    tests = [
        ("clustering_visualizer.py", test_clustering_visualizer),
        ("app.py", test_app_flask),
        ("index.html", test_index_html),
        ("Secuencia Completa", test_full_sequence)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: PASÓ")
            else:
                print(f"❌ {test_name}: FALLÓ")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
    
    print("\n" + "=" * 50)
    print("📊 RESUMEN DE SECUENCIA")
    print("=" * 50)
    print(f"✅ Pasos completados: {passed}/{total}")
    print(f"❌ Pasos fallidos: {total - passed}/{total}")
    
    if passed == total:
        print("\n🎉 SECUENCIA COMPLETA FUNCIONANDO")
        print("✅ La secuencia clustering_visualizer → app → index.html funciona")
        print("🚀 El ejecutable debería funcionar correctamente")
    else:
        print("\n⚠️ PROBLEMAS EN LA SECUENCIA")
        print("🔧 Corrija los errores antes de crear el ejecutable")
    
    print("=" * 50)
    input("Presione Enter para continuar...")

if __name__ == "__main__":
    main()
