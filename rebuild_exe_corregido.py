#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para reconstruir el ejecutable con todas las correcciones aplicadas
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def verificar_archivos_necesarios():
    """Verifica que todos los archivos necesarios estén presentes"""
    print("🔍 VERIFICANDO ARCHIVOS NECESARIOS")
    print("=" * 50)
    
    archivos_requeridos = [
        'main.py',
        'app.py', 
        'clustering_visualizer.py',
        'index.html',
        'styles.css',
        'script.js',
        'datos.csv',
        'build_exe.py'
    ]
    
    archivos_faltantes = []
    
    for archivo in archivos_requeridos:
        if os.path.exists(archivo):
            print(f"✅ {archivo}")
        else:
            print(f"❌ {archivo} - FALTANTE")
            archivos_faltantes.append(archivo)
    
    if archivos_faltantes:
        print(f"\n❌ Archivos faltantes: {archivos_faltantes}")
        return False
    
    print("\n✅ Todos los archivos necesarios están presentes")
    return True

def verificar_correcciones():
    """Verifica que las correcciones estén aplicadas"""
    print("\n🔧 VERIFICANDO CORRECCIONES APLICADAS")
    print("=" * 50)
    
    correcciones_verificadas = 0
    total_correcciones = 4
    
    # 1. Verificar inicialización de 'data' en app.py
    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'self.data = None  # Inicializar el atributo data' in content:
            print("✅ Corrección 1: Inicialización de atributo 'data'")
            correcciones_verificadas += 1
        else:
            print("❌ Corrección 1: Inicialización de atributo 'data' - NO APLICADA")
    except:
        print("❌ Corrección 1: Error leyendo app.py")
    
    # 2. Verificar detección automática de puerto en script.js
    try:
        with open('script.js', 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'detectServerPort' in content:
            print("✅ Corrección 2: Detección automática de puerto")
            correcciones_verificadas += 1
        else:
            print("❌ Corrección 2: Detección automática de puerto - NO APLICADA")
    except:
        print("❌ Corrección 2: Error leyendo script.js")
    
    # 3. Verificar manejo de errores robusto
    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'hasattr(clustering_system, \'data\') and clustering_system.data is not None' in content:
            print("✅ Corrección 3: Manejo de errores robusto")
            correcciones_verificadas += 1
        else:
            print("❌ Corrección 3: Manejo de errores robusto - NO APLICADA")
    except:
        print("❌ Corrección 3: Error verificando manejo de errores")
    
    # 4. Verificar endpoint de test-connection
    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        if '@app.route(\'/test-connection\', methods=[\'GET\'])' in content:
            print("✅ Corrección 4: Endpoint test-connection")
            correcciones_verificadas += 1
        else:
            print("❌ Corrección 4: Endpoint test-connection - NO APLICADA")
    except:
        print("❌ Corrección 4: Error verificando endpoint")
    
    print(f"\n📊 Correcciones aplicadas: {correcciones_verificadas}/{total_correcciones}")
    
    if correcciones_verificadas == total_correcciones:
        print("✅ TODAS LAS CORRECCIONES ESTÁN APLICADAS")
        return True
    else:
        print("⚠️ ALGUNAS CORRECCIONES FALTAN")
        return False

def limpiar_archivos_anteriores():
    """Limpia archivos de compilaciones anteriores"""
    print("\n🧹 LIMPIANDO ARCHIVOS ANTERIORES")
    print("=" * 50)
    
    directorios_a_limpiar = ['build', 'dist', '__pycache__']
    archivos_a_limpiar = ['*.spec']
    
    for directorio in directorios_a_limpiar:
        if os.path.exists(directorio):
            try:
                shutil.rmtree(directorio)
                print(f"✅ Eliminado directorio: {directorio}")
            except Exception as e:
                print(f"⚠️ Error eliminando {directorio}: {e}")
        else:
            print(f"📁 {directorio}: No existe")
    
    # Limpiar archivos .spec
    import glob
    for spec_file in glob.glob("*.spec"):
        try:
            os.remove(spec_file)
            print(f"✅ Eliminado archivo: {spec_file}")
        except Exception as e:
            print(f"⚠️ Error eliminando {spec_file}: {e}")

def ejecutar_build():
    """Ejecuta el script de construcción"""
    print("\n🔨 CONSTRUYENDO EJECUTABLE")
    print("=" * 50)
    
    try:
        print("🚀 Ejecutando build_exe.py...")

        # Usar encoding más robusto para Windows
        result = subprocess.run([sys.executable, 'build_exe.py'],
                              capture_output=True, text=True,
                              encoding='utf-8', errors='replace')

        if result.returncode == 0:
            print("✅ Construcción exitosa")
            print("\n📋 Salida del build:")
            if result.stdout:
                print(result.stdout)
            else:
                print("(Sin salida stdout)")
            return True
        else:
            print("❌ Error en la construcción")
            print(f"📋 Código de retorno: {result.returncode}")
            if result.stderr:
                print("📋 Error stderr:")
                print(result.stderr)
            if result.stdout:
                print("📋 Salida stdout:")
                print(result.stdout)
            return False

    except UnicodeDecodeError as e:
        print(f"❌ Error de codificación: {e}")
        print("🔧 Intentando con codificación alternativa...")
        try:
            result = subprocess.run([sys.executable, 'build_exe.py'],
                                  capture_output=True, text=True,
                                  encoding='cp1252', errors='replace')
            if result.returncode == 0:
                print("✅ Construcción exitosa con codificación alternativa")
                return True
            else:
                print("❌ Error persistente con codificación alternativa")
                return False
        except Exception as e2:
            print(f"❌ Error con codificación alternativa: {e2}")
            return False
    except Exception as e:
        print(f"❌ Error ejecutando build: {e}")
        return False

def verificar_ejecutable():
    """Verifica que el ejecutable se haya creado correctamente"""
    print("\n🔍 VERIFICANDO EJECUTABLE GENERADO")
    print("=" * 50)
    
    dist_dir = Path('dist')
    if not dist_dir.exists():
        print("❌ Directorio 'dist' no encontrado")
        return False
    
    # Buscar ejecutable
    exe_files = list(dist_dir.glob('*.exe'))
    if not exe_files:
        print("❌ Archivo ejecutable (.exe) no encontrado")
        return False
    
    exe_file = exe_files[0]
    print(f"✅ Ejecutable encontrado: {exe_file}")
    
    # Verificar tamaño
    size_mb = exe_file.stat().st_size / (1024 * 1024)
    print(f"📊 Tamaño: {size_mb:.1f} MB")
    
    if size_mb < 10:
        print("⚠️ Tamaño sospechosamente pequeño")
    elif size_mb > 500:
        print("⚠️ Tamaño muy grande")
    else:
        print("✅ Tamaño normal")
    
    return True

def main():
    """Función principal"""
    print("🔧 RECONSTRUCCIÓN DEL EJECUTABLE CON CORRECCIONES")
    print("🧠 Sistema Híbrido de Evaluación de Autismo")
    print("=" * 60)
    
    # Paso 1: Verificar archivos
    if not verificar_archivos_necesarios():
        print("\n❌ No se puede continuar sin todos los archivos")
        input("Presione Enter para salir...")
        return
    
    # Paso 2: Verificar correcciones
    if not verificar_correcciones():
        print("\n⚠️ Algunas correcciones no están aplicadas")
        respuesta = input("¿Continuar de todos modos? (s/N): ")
        if respuesta.lower() != 's':
            print("🛑 Construcción cancelada")
            return
    
    # Paso 3: Limpiar archivos anteriores
    limpiar_archivos_anteriores()
    
    # Paso 4: Construir ejecutable
    if not ejecutar_build():
        print("\n❌ Error en la construcción del ejecutable")
        input("Presione Enter para salir...")
        return
    
    # Paso 5: Verificar resultado
    if verificar_ejecutable():
        print("\n🎉 EJECUTABLE RECONSTRUIDO EXITOSAMENTE")
        print("=" * 60)
        print("✅ El ejecutable ha sido creado con todas las correcciones")
        print("🔧 Correcciones incluidas:")
        print("   • Inicialización correcta del atributo 'data'")
        print("   • Detección automática de puerto en JavaScript")
        print("   • Manejo de errores robusto")
        print("   • Endpoint de test de conexión")
        print("   • Verificación de salud del sistema mejorada")
        print("")
        print("🚀 El ejecutable debería funcionar sin errores en bucle")
        print("📁 Ubicación: dist/")
        print("=" * 60)
    else:
        print("\n❌ Problemas verificando el ejecutable")
    
    input("\nPresione Enter para salir...")

if __name__ == "__main__":
    main()
