#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para probar que la integración de gráficos funciona correctamente
"""

import os
import time
import threading
import webbrowser
import urllib.request
from main import AutismAssessmentApp

def test_archivos_graficos():
    """Verifica que los archivos de gráficos estén presentes"""
    print("🔍 VERIFICANDO ARCHIVOS DE GRÁFICOS")
    print("=" * 50)
    
    archivos_necesarios = [
        'PaginaGraficos/index.html',
        'PaginaGraficos/graficos_generados'
    ]
    
    todos_ok = True
    
    for archivo in archivos_necesarios:
        if os.path.exists(archivo):
            if os.path.isdir(archivo):
                png_count = len([f for f in os.listdir(archivo) if f.endswith('.png')])
                print(f"✅ {archivo}/ ({png_count} imágenes)")
            else:
                print(f"✅ {archivo}")
        else:
            print(f"❌ {archivo} - FALTANTE")
            todos_ok = False
    
    return todos_ok

def test_rutas_flask():
    """Prueba las rutas de Flask para gráficos"""
    print("\n🔍 PROBANDO RUTAS DE FLASK")
    print("=" * 50)
    
    try:
        from app import app
        
        with app.test_client() as client:
            # Test ruta principal de gráficos
            response = client.get('/graficos')
            if response.status_code == 200:
                print("✅ /graficos - Página principal de gráficos")
                
                # Verificar que contiene contenido HTML
                content = response.get_data(as_text=True)
                if 'Análisis de Clustering' in content:
                    print("  ✅ Contenido HTML correcto")
                else:
                    print("  ⚠️ Contenido HTML incompleto")
            else:
                print(f"❌ /graficos - Error {response.status_code}")
                return False
            
            # Test ruta de imagen
            response = client.get('/PaginaGraficos/graficos_generados/01_eda_distribuciones_numericas.png')
            if response.status_code == 200:
                print("✅ /PaginaGraficos/graficos_generados/01_eda_distribuciones_numericas.png")
                
                # Verificar que es una imagen
                if response.content_type.startswith('image/'):
                    print(f"  ✅ Tipo de contenido: {response.content_type}")
                else:
                    print(f"  ⚠️ Tipo de contenido inesperado: {response.content_type}")
            else:
                print(f"❌ Imagen de prueba - Error {response.status_code}")
                return False
            
            return True
            
    except Exception as e:
        print(f"❌ Error probando rutas Flask: {e}")
        return False

def test_javascript_graficos():
    """Verifica que la función JavaScript esté presente"""
    print("\n🔍 VERIFICANDO FUNCIÓN JAVASCRIPT")
    print("=" * 50)
    
    try:
        with open('script.js', 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'function abrirGraficos()' in content:
            print("✅ Función abrirGraficos() encontrada")
            
            if 'API_BASE_URL}/graficos' in content:
                print("✅ URL de gráficos correcta")
                return True
            else:
                print("❌ URL de gráficos incorrecta")
                return False
        else:
            print("❌ Función abrirGraficos() no encontrada")
            return False
            
    except Exception as e:
        print(f"❌ Error verificando JavaScript: {e}")
        return False

def test_servidor_completo():
    """Prueba el servidor completo con gráficos"""
    print("\n🔍 PROBANDO SERVIDOR COMPLETO CON GRÁFICOS")
    print("=" * 50)
    
    try:
        app = AutismAssessmentApp()
        
        # Iniciar servidor en hilo separado
        def run_server():
            try:
                app.run()
            except:
                pass
        
        server_thread = threading.Thread(target=run_server, daemon=True)
        server_thread.start()
        
        # Esperar a que el servidor inicie
        print("⏳ Esperando servidor...")
        time.sleep(5)
        
        if hasattr(app, 'port') and app.port:
            print(f"📍 Servidor iniciado en puerto: {app.port}")
            
            # Probar página principal
            try:
                url_principal = f"http://127.0.0.1:{app.port}/"
                request = urllib.request.Request(url_principal)
                with urllib.request.urlopen(request, timeout=5) as response:
                    if response.getcode() == 200:
                        print("✅ Página principal accesible")
                    else:
                        print(f"❌ Página principal error: {response.getcode()}")
                        return False
            except Exception as e:
                print(f"❌ Error accediendo página principal: {e}")
                return False
            
            # Probar página de gráficos
            try:
                url_graficos = f"http://127.0.0.1:{app.port}/graficos"
                request = urllib.request.Request(url_graficos)
                with urllib.request.urlopen(request, timeout=5) as response:
                    if response.getcode() == 200:
                        print("✅ Página de gráficos accesible")
                        
                        # Verificar contenido
                        content = response.read().decode('utf-8')
                        if 'Análisis de Clustering' in content:
                            print("✅ Contenido de gráficos correcto")
                        else:
                            print("⚠️ Contenido de gráficos incompleto")
                    else:
                        print(f"❌ Página de gráficos error: {response.getcode()}")
                        return False
            except Exception as e:
                print(f"❌ Error accediendo página de gráficos: {e}")
                return False
            
            # Probar imagen
            try:
                url_imagen = f"http://127.0.0.1:{app.port}/PaginaGraficos/graficos_generados/01_eda_distribuciones_numericas.png"
                request = urllib.request.Request(url_imagen)
                with urllib.request.urlopen(request, timeout=5) as response:
                    if response.getcode() == 200:
                        print("✅ Imagen accesible")
                        
                        # Verificar que es imagen
                        content_type = response.headers.get('Content-Type', '')
                        if content_type.startswith('image/'):
                            print(f"✅ Tipo de imagen correcto: {content_type}")
                        else:
                            print(f"⚠️ Tipo de contenido inesperado: {content_type}")
                    else:
                        print(f"❌ Imagen error: {response.getcode()}")
                        return False
            except Exception as e:
                print(f"❌ Error accediendo imagen: {e}")
                return False
            
            # Abrir navegador para verificación manual
            print("\n🌐 Abriendo navegador para verificación manual...")
            webbrowser.open(f"http://127.0.0.1:{app.port}", new=2)
            
            print("\n📋 VERIFICACIÓN MANUAL:")
            print("1. La página principal debería cargar correctamente")
            print("2. Haga clic en el botón 'Gráficos' en la navegación")
            print("3. Debería abrir una nueva pestaña con la página de gráficos")
            print("4. La página debe mostrar 10 gráficos de análisis")
            print("5. Las imágenes deben cargar correctamente")
            print("6. El botón 'Volver al Sistema Principal' debe funcionar")
            
            # Mantener servidor activo
            print(f"\n🔄 Servidor activo por 60 segundos para verificación...")
            for i in range(60):
                print(f"⏱️ {60-i} segundos restantes...", end='\r')
                time.sleep(1)
            
            print("\n✅ Prueba de servidor completa")
            return True
        else:
            print("❌ Servidor no pudo iniciar")
            return False
            
    except Exception as e:
        print(f"❌ Error en prueba de servidor: {e}")
        return False

def main():
    """Función principal de pruebas"""
    print("🔧 PRUEBA DE INTEGRACIÓN DE GRÁFICOS")
    print("🧠 Sistema de Evaluación de Autismo")
    print("=" * 60)
    
    tests = [
        ("Archivos de Gráficos", test_archivos_graficos),
        ("Rutas Flask", test_rutas_flask),
        ("JavaScript", test_javascript_graficos),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: PASÓ")
            else:
                print(f"❌ {test_name}: FALLÓ")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
    
    print("\n" + "=" * 60)
    print("📊 RESUMEN")
    print("=" * 60)
    print(f"✅ Pruebas básicas: {passed}/{total}")
    
    if passed == total:
        print("🎉 TODAS LAS PRUEBAS BÁSICAS EXITOSAS")
        
        respuesta = input("\n¿Ejecutar prueba completa del servidor con gráficos? (s/N): ")
        if respuesta.lower() == 's':
            print("\n🚀 INICIANDO PRUEBA COMPLETA...")
            if test_servidor_completo():
                print("\n🎉 INTEGRACIÓN DE GRÁFICOS COMPLETAMENTE FUNCIONAL")
                print("✅ El ejecutable debería mostrar gráficos correctamente")
                print("🔗 El botón 'Gráficos' abrirá la página con 10 visualizaciones")
            else:
                print("\n⚠️ Problemas en prueba completa del servidor")
        else:
            print("\n✅ Pruebas básicas completadas")
            print("🚀 La integración de gráficos debería funcionar")
    else:
        print("❌ PROBLEMAS EN PRUEBAS BÁSICAS")
        print("🔧 Revise los errores mostrados")
    
    print("=" * 60)
    input("Presione Enter para salir...")

if __name__ == "__main__":
    main()
