#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script fusionado para construir el ejecutable del Sistema de Evaluación de Autismo
Incluye todas las correcciones de conexión y manejo robusto de errores
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path
import glob

def verificar_archivos_necesarios():
    """Verifica que todos los archivos necesarios estén presentes"""
    print("🔍 VERIFICANDO ARCHIVOS NECESARIOS")
    print("=" * 50)
    
    archivos_requeridos = [
        'main.py',
        'app.py',
        'clustering_visualizer.py',
        'index.html',
        'styles.css',
        'script.js',
        'datos.csv'
    ]

    # Verificar también la carpeta de gráficos
    carpetas_requeridas = [
        'PaginaGraficos'
    ]
    
    archivos_faltantes = []

    for archivo in archivos_requeridos:
        if os.path.exists(archivo):
            print(f"✅ {archivo}")
        else:
            print(f"❌ {archivo} - FALTANTE")
            archivos_faltantes.append(archivo)

    # Verificar carpetas
    for carpeta in carpetas_requeridas:
        if os.path.exists(carpeta) and os.path.isdir(carpeta):
            print(f"✅ {carpeta}/ (carpeta)")
            # Verificar contenido de PaginaGraficos
            if carpeta == 'PaginaGraficos':
                index_path = os.path.join(carpeta, 'index.html')
                graficos_path = os.path.join(carpeta, 'graficos_generados')
                if os.path.exists(index_path):
                    print(f"  ✅ {index_path}")
                else:
                    print(f"  ❌ {index_path} - FALTANTE")
                    archivos_faltantes.append(index_path)
                if os.path.exists(graficos_path) and os.path.isdir(graficos_path):
                    png_files = [f for f in os.listdir(graficos_path) if f.endswith('.png')]
                    print(f"  ✅ {graficos_path}/ ({len(png_files)} imágenes)")

                    # Verificar imágenes específicas
                    imagenes_requeridas = [
                        '01_eda_distribuciones_numericas.png',
                        '02_eda_variables_categoricas.png',
                        '03_eda_matriz_correlacion.png',
                        '04_eda_analisis_grupos_riesgo.png',
                        '05_clustering_inicial_pca.png',
                        '06_clustering_inicial_tsne.png',
                        '07_comparacion_antes_despues.png',
                        '08_importancia_caracteristicas.png',
                        '09_arboles_decision_comparacion.png',
                        '10_analisis_clusters_final.png'
                    ]

                    for imagen in imagenes_requeridas:
                        imagen_path = os.path.join(graficos_path, imagen)
                        if os.path.exists(imagen_path):
                            size_kb = os.path.getsize(imagen_path) // 1024
                            print(f"    ✅ {imagen} ({size_kb} KB)")
                        else:
                            print(f"    ❌ {imagen} - FALTANTE")
                            archivos_faltantes.append(imagen_path)
                else:
                    print(f"  ❌ {graficos_path}/ - FALTANTE")
                    archivos_faltantes.append(graficos_path)
        else:
            print(f"❌ {carpeta}/ - CARPETA FALTANTE")
            archivos_faltantes.append(carpeta)

    if archivos_faltantes:
        print(f"\n❌ Archivos/carpetas faltantes: {archivos_faltantes}")
        return False

    print("\n✅ Todos los archivos y carpetas necesarios están presentes")
    return True

def verificar_correcciones():
    """Verifica que las correcciones estén aplicadas"""
    print("\n🔧 VERIFICANDO CORRECCIONES APLICADAS")
    print("=" * 50)
    
    correcciones_verificadas = 0
    total_correcciones = 4
    
    # 1. Verificar inicialización de 'data' en app.py
    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'self.data = None  # Inicializar el atributo data' in content:
            print("✅ Corrección 1: Inicialización de atributo 'data'")
            correcciones_verificadas += 1
        else:
            print("❌ Corrección 1: Inicialización de atributo 'data' - NO APLICADA")
    except:
        print("❌ Corrección 1: Error leyendo app.py")
    
    # 2. Verificar detección automática de puerto en script.js
    try:
        with open('script.js', 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'detectServerPort' in content:
            print("✅ Corrección 2: Detección automática de puerto")
            correcciones_verificadas += 1
        else:
            print("❌ Corrección 2: Detección automática de puerto - NO APLICADA")
    except:
        print("❌ Corrección 2: Error leyendo script.js")
    
    # 3. Verificar manejo de errores robusto
    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'hasattr(clustering_system, \'data\') and clustering_system.data is not None' in content:
            print("✅ Corrección 3: Manejo de errores robusto")
            correcciones_verificadas += 1
        else:
            print("❌ Corrección 3: Manejo de errores robusto - NO APLICADA")
    except:
        print("❌ Corrección 3: Error verificando manejo de errores")
    
    # 4. Verificar endpoint de test-connection
    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        if '@app.route(\'/test-connection\', methods=[\'GET\'])' in content:
            print("✅ Corrección 4: Endpoint test-connection")
            correcciones_verificadas += 1
        else:
            print("❌ Corrección 4: Endpoint test-connection - NO APLICADA")
    except:
        print("❌ Corrección 4: Error verificando endpoint")
    
    print(f"\n📊 Correcciones aplicadas: {correcciones_verificadas}/{total_correcciones}")
    
    if correcciones_verificadas == total_correcciones:
        print("✅ TODAS LAS CORRECCIONES ESTÁN APLICADAS")
        return True
    else:
        print("⚠️ ALGUNAS CORRECCIONES FALTAN")
        return False

def verificar_dependencias():
    """Verifica que las dependencias estén instaladas"""
    print("\n🔍 VERIFICANDO DEPENDENCIAS")
    print("=" * 50)
    
    dependencias = [
        ('flask', 'Flask'),
        ('pandas', 'pandas'), 
        ('numpy', 'numpy'),
        ('sklearn', 'scikit-learn'),
        ('matplotlib', 'matplotlib'),
        ('seaborn', 'seaborn')
    ]
    
    faltantes = []
    
    for modulo, nombre in dependencias:
        try:
            __import__(modulo)
            print(f"✅ {nombre}")
        except ImportError:
            print(f"❌ {nombre} - NO INSTALADO")
            faltantes.append(nombre)
    
    if faltantes:
        print(f"\n❌ Dependencias faltantes: {faltantes}")
        print("🔧 Instalando dependencias faltantes...")
        try:
            for _, nombre in dependencias:
                if nombre in faltantes:
                    subprocess.check_call([sys.executable, "-m", "pip", "install", nombre])
            print("✅ Dependencias instaladas")
            return True
        except:
            print("❌ Error instalando dependencias")
            return False
    
    print("\n✅ Todas las dependencias están instaladas")
    return True

def verificar_pyinstaller():
    """Verifica que PyInstaller esté instalado"""
    print("\n🔍 VERIFICANDO PYINSTALLER")
    print("=" * 50)
    
    try:
        import PyInstaller
        print(f"✅ PyInstaller encontrado: {PyInstaller.__version__}")
        return True
    except ImportError:
        print("❌ PyInstaller no encontrado")
        print("🔧 Instalando PyInstaller...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
            print("✅ PyInstaller instalado correctamente")
            return True
        except subprocess.CalledProcessError:
            print("❌ Error instalando PyInstaller")
            return False

def limpiar_archivos_anteriores():
    """Limpia archivos de compilaciones anteriores"""
    print("\n🧹 LIMPIANDO ARCHIVOS ANTERIORES")
    print("=" * 50)
    
    directorios_a_limpiar = ['build', 'dist', '__pycache__']
    
    for directorio in directorios_a_limpiar:
        if os.path.exists(directorio):
            try:
                shutil.rmtree(directorio)
                print(f"✅ Eliminado directorio: {directorio}")
            except Exception as e:
                print(f"⚠️ Error eliminando {directorio}: {e}")
        else:
            print(f"📁 {directorio}: No existe")
    
    # Limpiar archivos .spec
    for spec_file in glob.glob("*.spec"):
        try:
            os.remove(spec_file)
            print(f"✅ Eliminado archivo: {spec_file}")
        except Exception as e:
            print(f"⚠️ Error eliminando {spec_file}: {e}")

def construir_ejecutable():
    """Construye el ejecutable usando PyInstaller con manejo robusto"""
    print("\n🔨 CONSTRUYENDO EJECUTABLE")
    print("=" * 50)
    
    try:
        # Comando de PyInstaller optimizado con imágenes específicas
        cmd = [
            'pyinstaller',
            '--onefile',
            '--name=SistemaAutismo',
            '--add-data=datos.csv;.',
            '--add-data=index.html;.',
            '--add-data=styles.css;.',
            '--add-data=script.js;.',
            '--add-data=PaginaGraficos/index.html;PaginaGraficos',
            '--add-data=PaginaGraficos/graficos_generados/01_eda_distribuciones_numericas.png;PaginaGraficos/graficos_generados',
            '--add-data=PaginaGraficos/graficos_generados/02_eda_variables_categoricas.png;PaginaGraficos/graficos_generados',
            '--add-data=PaginaGraficos/graficos_generados/03_eda_matriz_correlacion.png;PaginaGraficos/graficos_generados',
            '--add-data=PaginaGraficos/graficos_generados/04_eda_analisis_grupos_riesgo.png;PaginaGraficos/graficos_generados',
            '--add-data=PaginaGraficos/graficos_generados/05_clustering_inicial_pca.png;PaginaGraficos/graficos_generados',
            '--add-data=PaginaGraficos/graficos_generados/06_clustering_inicial_tsne.png;PaginaGraficos/graficos_generados',
            '--add-data=PaginaGraficos/graficos_generados/07_comparacion_antes_despues.png;PaginaGraficos/graficos_generados',
            '--add-data=PaginaGraficos/graficos_generados/08_importancia_caracteristicas.png;PaginaGraficos/graficos_generados',
            '--add-data=PaginaGraficos/graficos_generados/09_arboles_decision_comparacion.png;PaginaGraficos/graficos_generados',
            '--add-data=PaginaGraficos/graficos_generados/10_analisis_clusters_final.png;PaginaGraficos/graficos_generados',
            # Imports ocultos para sklearn
            '--hidden-import=sklearn.utils._cython_blas',
            '--hidden-import=sklearn.neighbors.typedefs',
            '--hidden-import=sklearn.neighbors.quad_tree',
            '--hidden-import=sklearn.tree._utils',
            '--hidden-import=sklearn.cluster._dbscan_inner',
            '--hidden-import=sklearn.cluster._k_means_fast',
            '--hidden-import=sklearn.utils._heap',
            '--hidden-import=sklearn.utils._sorting',
            '--hidden-import=sklearn.utils._vector_sentinel',
            # Imports ocultos para matplotlib
            '--hidden-import=matplotlib.backends.backend_agg',
            '--hidden-import=matplotlib.figure',
            '--hidden-import=matplotlib.pyplot',
            # Imports ocultos para pandas
            '--hidden-import=pandas._libs.tslibs.timedeltas',
            '--hidden-import=pandas._libs.tslibs.np_datetime',
            '--hidden-import=pandas._libs.skiplist',
            # Imports ocultos para numpy
            '--hidden-import=numpy.random.common',
            '--hidden-import=numpy.random.bounded_integers',
            '--hidden-import=numpy.random.entropy',
            # Configuración
            '--console',
            '--noconfirm',
            'main.py'
        ]
        
        print("🚀 Ejecutando PyInstaller...")
        print(f"Comando: pyinstaller --onefile --name=SistemaAutismo ...")
        
        # Ejecutar con manejo robusto de codificación
        result = subprocess.run(cmd, capture_output=True, text=True, 
                              encoding='utf-8', errors='replace', timeout=600)
        
        if result.returncode == 0:
            print("✅ Construcción exitosa")
            return True
        else:
            print(f"❌ Error en construcción (código: {result.returncode})")
            if result.stderr:
                print("📋 Errores:")
                # Mostrar solo las últimas líneas del error para no saturar
                error_lines = result.stderr.split('\n')[-10:]
                for line in error_lines:
                    if line.strip():
                        print(f"   {line}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Timeout en construcción (10 minutos)")
        return False
    except Exception as e:
        print(f"❌ Error ejecutando PyInstaller: {e}")
        return False

def verificar_ejecutable():
    """Verifica que el ejecutable se haya creado correctamente"""
    print("\n🔍 VERIFICANDO EJECUTABLE GENERADO")
    print("=" * 50)
    
    dist_dir = Path('dist')
    if not dist_dir.exists():
        print("❌ Directorio 'dist' no encontrado")
        return False
    
    # Buscar ejecutable
    exe_files = list(dist_dir.glob('*.exe'))
    if not exe_files:
        print("❌ Archivo ejecutable (.exe) no encontrado")
        return False
    
    exe_file = exe_files[0]
    print(f"✅ Ejecutable encontrado: {exe_file}")
    
    # Verificar tamaño
    size_mb = exe_file.stat().st_size / (1024 * 1024)
    print(f"📊 Tamaño: {size_mb:.1f} MB")
    
    if size_mb < 10:
        print("⚠️ Tamaño sospechosamente pequeño")
    elif size_mb > 500:
        print("⚠️ Tamaño muy grande")
    else:
        print("✅ Tamaño normal")
    
    return True

def crear_readme():
    """Crea documentación para el ejecutable"""
    print("\n📋 CREANDO DOCUMENTACIÓN")
    print("=" * 50)
    
    readme_content = """# Sistema Híbrido de Evaluación de Autismo - Ejecutable

## CORRECCIONES INCLUIDAS
✅ Inicialización correcta del atributo 'data'
✅ Detección automática de puerto en JavaScript  
✅ Manejo de errores robusto con hasattr()
✅ Endpoint /test-connection para verificación
✅ Sistema de respaldo BasicClusteringSystem
✅ Health check mejorado con diagnósticos
✅ Sin errores en bucle de conexión

## Uso
1. Ejecute SistemaAutismo.exe
2. La aplicación detectará automáticamente el puerto disponible
3. Se abrirá el navegador automáticamente
4. Complete el cuestionario de 10 preguntas
5. Obtenga resultados con clustering interactivo

## Características
- Aplicación independiente (no requiere Python)
- Detección automática de puerto del servidor
- Conexión estable sin errores en bucle
- Clustering DBSCAN + GMM + ML
- Visualizaciones interactivas
- Análisis para edades 1-16 años

## Importante
Esta herramienta es para detección temprana.
No reemplaza el diagnóstico profesional.
Consulte siempre con un especialista.
"""
    
    try:
        with open('README_EJECUTABLE_CORREGIDO.txt', 'w', encoding='utf-8') as f:
            f.write(readme_content)
        print("✅ README creado")
        return True
    except Exception as e:
        print(f"⚠️ Error creando README: {e}")
        return False

def main():
    """Función principal del constructor fusionado"""
    print("🔧 CONSTRUCTOR DE EJECUTABLE FUSIONADO")
    print("🧠 Sistema Híbrido de Evaluación de Autismo")
    print("🔗 CON TODAS LAS CORRECCIONES DE CONEXIÓN")
    print("=" * 60)
    
    # Verificaciones previas
    verificaciones = [
        ("Archivos Necesarios", verificar_archivos_necesarios),
        ("Correcciones Aplicadas", verificar_correcciones),
        ("Dependencias Python", verificar_dependencias),
        ("PyInstaller", verificar_pyinstaller),
    ]
    
    for nombre, func in verificaciones:
        print(f"\n{'='*20} {nombre} {'='*20}")
        if not func():
            print(f"\n❌ Error en {nombre}")
            respuesta = input("¿Continuar de todos modos? (s/N): ")
            if respuesta.lower() != 's':
                print("🛑 Construcción cancelada")
                return False
    
    # Proceso de construcción
    print(f"\n{'='*20} CONSTRUCCIÓN {'='*20}")
    
    # Limpiar archivos anteriores
    limpiar_archivos_anteriores()
    
    # Construir ejecutable
    if not construir_ejecutable():
        print("\n❌ Error en la construcción del ejecutable")
        input("Presione Enter para salir...")
        return False
    
    # Verificar resultado
    if not verificar_ejecutable():
        print("\n❌ Problemas verificando el ejecutable")
        input("Presione Enter para salir...")
        return False
    
    # Crear documentación
    crear_readme()
    
    # Éxito
    print("\n" + "=" * 60)
    print("🎉 EJECUTABLE CREADO EXITOSAMENTE")
    print("=" * 60)
    print("📁 Ubicación: dist/SistemaAutismo.exe")
    print("📋 Documentación: README_EJECUTABLE_CORREGIDO.txt")
    print("")
    print("✅ CORRECCIONES INCLUIDAS:")
    print("   • Sin errores en bucle del atributo 'data'")
    print("   • Detección automática de puerto")
    print("   • Conexión estable frontend-backend")
    print("   • Manejo robusto de errores")
    print("   • Sistema de respaldo integrado")
    print("")
    print("🚀 El ejecutable está listo para usar")
    print("🔗 NO debería mostrar 'Sin conexión - Modo local'")
    print("=" * 60)
    
    input("\nPresione Enter para salir...")
    return True

if __name__ == "__main__":
    main()
