#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de diagnóstico simple y rápido que no se cuelga
"""

import sys
import os
import time
import threading
from pathlib import Path

def test_archivos():
    """Verifica archivos básicos"""
    print("🔍 VERIFICANDO ARCHIVOS")
    print("=" * 30)
    
    archivos = ['app.py', 'main.py', 'index.html', 'script.js', 'styles.css']
    todos_ok = True
    
    for archivo in archivos:
        if os.path.exists(archivo):
            print(f"✅ {archivo}")
        else:
            print(f"❌ {archivo}")
            todos_ok = False
    
    return todos_ok

def test_importacion_basica():
    """Prueba importación básica sin ejecutar nada complejo"""
    print("\n🔍 VERIFICANDO IMPORTACIONES")
    print("=" * 30)
    
    try:
        print("📦 Probando importar sys, os...")
        import sys, os
        print("✅ Módulos básicos OK")
        
        print("📦 Probando importar Flask...")
        from flask import Flask
        print("✅ Flask OK")
        
        print("📦 Probando importar pandas...")
        import pandas as pd
        print("✅ Pandas OK")
        
        print("📦 Probando importar sklearn...")
        from sklearn.cluster import DBSCAN
        print("✅ Sklearn OK")
        
        return True
        
    except Exception as e:
        print(f"❌ Error en importaciones: {e}")
        return False

def test_app_syntax():
    """Verifica que app.py tenga sintaxis correcta sin ejecutarlo"""
    print("\n🔍 VERIFICANDO SINTAXIS DE APP.PY")
    print("=" * 30)
    
    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Compilar para verificar sintaxis
        compile(content, 'app.py', 'exec')
        print("✅ Sintaxis de app.py correcta")
        
        # Verificar correcciones específicas
        if 'self.data = None' in content:
            print("✅ Corrección 'data' encontrada")
        else:
            print("❌ Corrección 'data' no encontrada")
        
        if 'hasattr(clustering_system, \'data\')' in content:
            print("✅ Verificación hasattr encontrada")
        else:
            print("❌ Verificación hasattr no encontrada")
        
        return True
        
    except SyntaxError as e:
        print(f"❌ Error de sintaxis en app.py: {e}")
        return False
    except Exception as e:
        print(f"❌ Error leyendo app.py: {e}")
        return False

def test_script_js():
    """Verifica script.js"""
    print("\n🔍 VERIFICANDO SCRIPT.JS")
    print("=" * 30)
    
    try:
        with open('script.js', 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'detectServerPort' in content:
            print("✅ Función detectServerPort encontrada")
        else:
            print("❌ Función detectServerPort no encontrada")
        
        if 'API_BASE_URL' in content:
            print("✅ Variable API_BASE_URL encontrada")
        else:
            print("❌ Variable API_BASE_URL no encontrada")
        
        if 'checkBackendConnection' in content:
            print("✅ Función checkBackendConnection encontrada")
        else:
            print("❌ Función checkBackendConnection no encontrada")
        
        return True
        
    except Exception as e:
        print(f"❌ Error leyendo script.js: {e}")
        return False

def test_importacion_controlada():
    """Intenta importar app.py de forma controlada"""
    print("\n🔍 VERIFICANDO IMPORTACIÓN CONTROLADA")
    print("=" * 30)
    
    import_success = False
    error_msg = ""
    
    def try_import():
        nonlocal import_success, error_msg
        try:
            # Cambiar al directorio correcto
            original_path = sys.path.copy()
            current_dir = os.getcwd()
            if current_dir not in sys.path:
                sys.path.insert(0, current_dir)
            
            print("📦 Intentando importar app...")
            
            # Importar solo lo necesario
            import app
            print("✅ app.py importado")
            
            # Verificar que existe la variable clustering_system
            if hasattr(app, 'clustering_system'):
                print("✅ clustering_system encontrado")
                
                # Verificar tipo
                cs_type = type(app.clustering_system).__name__
                print(f"📊 Tipo: {cs_type}")
                
                # Verificar atributo data de forma segura
                if hasattr(app.clustering_system, 'data'):
                    print("✅ Atributo 'data' existe")
                    if app.clustering_system.data is not None:
                        print(f"✅ Datos cargados: {len(app.clustering_system.data)} casos")
                    else:
                        print("⚠️ Atributo 'data' es None")
                else:
                    print("❌ Atributo 'data' no existe")
            else:
                print("❌ clustering_system no encontrado")
            
            import_success = True
            
        except Exception as e:
            error_msg = str(e)
            print(f"❌ Error importando: {e}")
        finally:
            # Restaurar path
            sys.path = original_path
    
    # Ejecutar en hilo con timeout
    import_thread = threading.Thread(target=try_import)
    import_thread.daemon = True
    import_thread.start()
    import_thread.join(timeout=15)  # 15 segundos timeout
    
    if import_thread.is_alive():
        print("❌ Timeout importando app.py (15s)")
        return False
    
    if import_success:
        print("✅ Importación exitosa")
        return True
    else:
        print(f"❌ Importación falló: {error_msg}")
        return False

def main():
    """Función principal de diagnóstico rápido"""
    print("🔧 DIAGNÓSTICO RÁPIDO Y SIMPLE")
    print("🧠 Sistema de Evaluación de Autismo")
    print("=" * 50)
    
    tests = [
        ("Archivos", test_archivos),
        ("Importaciones Básicas", test_importacion_basica),
        ("Sintaxis app.py", test_app_syntax),
        ("Script.js", test_script_js),
        ("Importación Controlada", test_importacion_controlada),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*15} {test_name} {'='*15}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: PASÓ")
            else:
                print(f"❌ {test_name}: FALLÓ")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
    
    print("\n" + "=" * 50)
    print("📊 RESUMEN")
    print("=" * 50)
    print(f"✅ Pruebas pasadas: {passed}/{total}")
    
    if passed >= 4:  # Al menos 4 de 5 pruebas
        print("🎉 SISTEMA EN BUEN ESTADO")
        print("✅ Debería funcionar correctamente")
        print("🚀 Puede proceder a reconstruir el ejecutable")
    elif passed >= 2:
        print("⚠️ SISTEMA CON PROBLEMAS MENORES")
        print("🔧 Revise los errores mostrados")
        print("⚠️ Puede intentar reconstruir pero con precaución")
    else:
        print("❌ SISTEMA CON PROBLEMAS GRAVES")
        print("🔧 Debe corregir los errores antes de continuar")
    
    print("=" * 50)
    
    # Recomendaciones
    if passed < total:
        print("\n🔧 RECOMENDACIONES:")
        if passed < 2:
            print("1. Verifique que todos los archivos estén presentes")
            print("2. Reinstale las dependencias: pip install -r requirements.txt")
            print("3. Verifique que no hay errores de sintaxis")
        else:
            print("1. Los problemas parecen menores")
            print("2. Puede intentar reconstruir el ejecutable")
            print("3. Si persisten problemas, revise los logs detallados")
    
    input("\nPresione Enter para salir...")

if __name__ == "__main__":
    main()
