#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Constructor de ejecutable con enfoque específico para imágenes
Soluciona el problema de imágenes no incluidas en el ejecutable
"""

import os
import sys
import subprocess
import shutil
import glob
from pathlib import Path

def verificar_archivos_completo():
    """Verificación exhaustiva de archivos incluyendo imágenes"""
    print("🔍 VERIFICANDO ARCHIVOS NECESARIOS")
    print("=" * 50)
    
    archivos_requeridos = [
        'main.py',
        'app.py', 
        'clustering_visualizer.py',
        'index.html',
        'styles.css',
        'script.js',
        'datos.csv'
    ]
    
    archivos_faltantes = []
    
    for archivo in archivos_requeridos:
        if os.path.exists(archivo):
            print(f"✅ {archivo}")
        else:
            print(f"❌ {archivo} - FALTANTE")
            archivos_faltantes.append(archivo)
    
    # Verificar PaginaGraficos
    if os.path.exists('PaginaGraficos') and os.path.isdir('PaginaGraficos'):
        print(f"✅ PaginaGraficos/ (carpeta)")
        
        # Verificar index.html
        index_path = 'PaginaGraficos/index.html'
        if os.path.exists(index_path):
            print(f"  ✅ {index_path}")
        else:
            print(f"  ❌ {index_path} - FALTANTE")
            archivos_faltantes.append(index_path)
        
        # Verificar carpeta de gráficos
        graficos_path = 'PaginaGraficos/graficos_generados'
        if os.path.exists(graficos_path) and os.path.isdir(graficos_path):
            # Buscar todas las imágenes PNG
            png_files = glob.glob(os.path.join(graficos_path, '*.png'))
            print(f"  ✅ {graficos_path}/ ({len(png_files)} imágenes PNG)")
            
            # Listar cada imagen con su tamaño
            for png_file in sorted(png_files):
                filename = os.path.basename(png_file)
                size_kb = os.path.getsize(png_file) // 1024
                print(f"    ✅ {filename} ({size_kb} KB)")
                
                # Verificar que no esté vacía
                if size_kb == 0:
                    print(f"    ⚠️ {filename} está vacía!")
                    archivos_faltantes.append(png_file)
            
            if len(png_files) < 10:
                print(f"    ⚠️ Se esperaban 10 imágenes, encontradas {len(png_files)}")
        else:
            print(f"  ❌ {graficos_path}/ - FALTANTE")
            archivos_faltantes.append(graficos_path)
    else:
        print(f"❌ PaginaGraficos/ - CARPETA FALTANTE")
        archivos_faltantes.append('PaginaGraficos')
    
    if archivos_faltantes:
        print(f"\n❌ Archivos/carpetas faltantes: {archivos_faltantes}")
        return False
    
    print("\n✅ Todos los archivos y carpetas necesarios están presentes")
    return True

def crear_spec_personalizado():
    """Crea un archivo .spec personalizado para mejor control"""
    print("\n🔧 CREANDO ARCHIVO .SPEC PERSONALIZADO")
    print("=" * 50)
    
    # Obtener todas las imágenes PNG
    png_files = glob.glob('PaginaGraficos/graficos_generados/*.png')
    
    # Crear lista de datos para el .spec
    datas_list = [
        "('datos.csv', '.')",
        "('index.html', '.')",
        "('styles.css', '.')",
        "('script.js', '.')",
        "('PaginaGraficos/index.html', 'PaginaGraficos')",
    ]

    # Agregar cada imagen PNG individualmente
    for png_file in png_files:
        # Convertir path a formato Windows si es necesario
        src_path = png_file.replace('\\', '/')
        dst_path = 'PaginaGraficos/graficos_generados'
        datas_list.append(f"('{src_path}', '{dst_path}')")

    # Crear el contenido del .spec sin f-string problemático
    datas_joined = ',\n        '.join(datas_list)

    spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        {datas_joined}
    ],
    hiddenimports=[
        'flask',
        'pandas',
        'numpy',
        'scikit-learn',
        'matplotlib',
        'seaborn',
        'sklearn.cluster',
        'sklearn.preprocessing',
        'sklearn.decomposition',
        'sklearn.metrics',
        'sklearn.neighbors'
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='SistemaAutismo',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    cofile=None,
    icon=None,
)
'''
    
    with open('SistemaAutismo_con_imagenes.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ Archivo .spec creado: SistemaAutismo_con_imagenes.spec")
    print(f"📊 Imágenes incluidas: {len(png_files)}")
    
    return True

def construir_ejecutable():
    """Construye el ejecutable usando el archivo .spec"""
    print("\n🔨 CONSTRUYENDO EJECUTABLE CON IMÁGENES")
    print("=" * 50)
    
    try:
        # Limpiar archivos anteriores
        if os.path.exists('build'):
            shutil.rmtree('build')
            print("🧹 Directorio build eliminado")
        
        if os.path.exists('dist'):
            shutil.rmtree('dist')
            print("🧹 Directorio dist eliminado")
        
        # Ejecutar PyInstaller con el archivo .spec
        cmd = ['pyinstaller', 'SistemaAutismo_con_imagenes.spec']
        
        print(f"🚀 Ejecutando: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✅ Construcción exitosa")
            return True
        else:
            print("❌ Error en construcción:")
            print(result.stdout)
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Error ejecutando PyInstaller: {e}")
        return False

def verificar_ejecutable():
    """Verifica que el ejecutable se creó correctamente"""
    print("\n🔍 VERIFICANDO EJECUTABLE GENERADO")
    print("=" * 50)
    
    exe_path = 'dist/SistemaAutismo.exe'
    
    if os.path.exists(exe_path):
        size_mb = os.path.getsize(exe_path) / (1024 * 1024)
        print(f"✅ Ejecutable encontrado: {exe_path}")
        print(f"📊 Tamaño: {size_mb:.1f} MB")
        
        if size_mb > 50:  # Debería ser más grande con las imágenes
            print("✅ Tamaño apropiado (incluye imágenes)")
            return True
        else:
            print("⚠️ Tamaño pequeño, posiblemente faltan imágenes")
            return False
    else:
        print(f"❌ Ejecutable no encontrado: {exe_path}")
        return False

def crear_documentacion():
    """Crea documentación del ejecutable con imágenes"""
    print("\n📋 CREANDO DOCUMENTACIÓN")
    print("=" * 50)
    
    doc_content = """
🧠 SISTEMA HÍBRIDO DE EVALUACIÓN DE AUTISMO
============================================
📁 Ejecutable con Gráficos Integrados

✅ CARACTERÍSTICAS INCLUIDAS:
• Puerto fijo 5000 (o alternativo 5001-5005)
• Página de gráficos completamente integrada
• 10 imágenes PNG de análisis incluidas
• Navegación entre sistema principal y gráficos
• Interfaz web responsive y moderna

🚀 INSTRUCCIONES DE USO:
1. Ejecutar SistemaAutismo.exe
2. El navegador se abrirá automáticamente
3. Usar el sistema de evaluación normalmente
4. Hacer clic en "Gráficos" para ver visualizaciones
5. Las imágenes deben cargar correctamente

📊 GRÁFICOS INCLUIDOS:
01. Distribución de Variables Numéricas Clave
02. Distribución de Variables Categóricas  
03. Matriz de Correlación - Variables Numéricas
04. Análisis por Grupos de Riesgo Verdaderos
05. Clusters Iniciales - Visualización PCA
06. Clusters Iniciales - Visualización t-SNE
07. Comparación Antes vs Después - Enfoque DBSCAN
08. Importancia de Características
09. Árboles de Decisión - Ensemble vs DBSCAN
10. Análisis de Clusters Final

🔧 SOLUCIÓN DE PROBLEMAS:
• Si las imágenes no cargan: Verificar que el puerto sea 5000
• Si hay error de conexión: Reiniciar el ejecutable
• Si falta alguna imagen: Contactar soporte técnico

📅 Generado: """ + str(Path().absolute()) + """
🔗 Versión: Ejecutable con Imágenes Integradas
"""
    
    with open('README_EJECUTABLE_CON_IMAGENES.txt', 'w', encoding='utf-8') as f:
        f.write(doc_content)
    
    print("✅ Documentación creada: README_EJECUTABLE_CON_IMAGENES.txt")

def main():
    """Función principal"""
    print("🔧 CONSTRUCTOR DE EJECUTABLE CON IMÁGENES")
    print("🧠 Sistema Híbrido de Evaluación de Autismo")
    print("🖼️ SOLUCIÓN PARA IMÁGENES NO INCLUIDAS")
    print("=" * 60)
    
    # Verificar archivos
    if not verificar_archivos_completo():
        print("\n❌ Faltan archivos necesarios")
        input("Presione Enter para salir...")
        return
    
    # Crear archivo .spec personalizado
    if not crear_spec_personalizado():
        print("\n❌ Error creando archivo .spec")
        input("Presione Enter para salir...")
        return
    
    # Construir ejecutable
    if not construir_ejecutable():
        print("\n❌ Error construyendo ejecutable")
        input("Presione Enter para salir...")
        return
    
    # Verificar ejecutable
    if not verificar_ejecutable():
        print("\n⚠️ Ejecutable creado pero con posibles problemas")
    
    # Crear documentación
    crear_documentacion()
    
    print("\n" + "=" * 60)
    print("🎉 EJECUTABLE CON IMÁGENES CREADO")
    print("=" * 60)
    print("📁 Ubicación: dist/SistemaAutismo.exe")
    print("📋 Documentación: README_EJECUTABLE_CON_IMAGENES.txt")
    print("🖼️ Imágenes: Incluidas específicamente")
    print("🔗 Puerto: 5000 (fijo)")
    print("=" * 60)
    
    input("Presione Enter para salir...")

if __name__ == "__main__":
    main()
