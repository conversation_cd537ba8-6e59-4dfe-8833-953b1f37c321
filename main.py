#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Sistema Híbrido de Evaluación de Autismo - Ejecutable de Escritorio
Versión ejecutable independiente que combina servidor Flask y navegador web
"""

import os
import sys
import threading
import time
import socket
import webbrowser
import signal
from pathlib import Path

# Configurar el path para el ejecutable
if getattr(sys, 'frozen', False):
    # Ejecutándose como ejecutable empaquetado
    BASE_DIR = Path(sys._MEIPASS)
    EXECUTABLE_MODE = True
else:
    # Ejecutándose como script Python
    BASE_DIR = Path(__file__).parent
    EXECUTABLE_MODE = False

# Agregar el directorio base al path de Python
sys.path.insert(0, str(BASE_DIR))

# Configurar variables de entorno para matplotlib
os.environ['MPLBACKEND'] = 'Agg'

# Importar y verificar módulos en la secuencia correcta
try:
    # 1. Primero importar clustering_visualizer
    print("🔧 Importando sistema de clustering...")
    import clustering_visualizer
    print("✅ Sistema de clustering importado correctamente")

    # 2. Luego importar la aplicación Flask (que usa clustering_visualizer)
    print("🔧 Importando aplicación Flask...")
    from app import app, clustering_system
    print("✅ Aplicación Flask importada correctamente")

except ImportError as e:
    print(f"❌ Error importando módulos: {e}")
    print("🔧 Verifique que todos los archivos estén presentes:")
    print("   • clustering_visualizer.py")
    print("   • app.py")
    print("   • datos.csv")
    sys.exit(1)

class AutismAssessmentApp:
    def __init__(self):
        self.server_thread = None
        self.port = None
        self.running = False
        self.clustering_initialized = False
        
    def find_free_port(self):
        """Encuentra un puerto libre para el servidor"""
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(('', 0))
            s.listen(1)
            port = s.getsockname()[1]
        return port

    def initialize_clustering_system(self):
        """Inicializa el sistema de clustering siguiendo la secuencia correcta"""
        print("🧠 Inicializando sistema de clustering...")

        try:
            # 1. Verificar que clustering_visualizer funcione
            print("🔧 Probando clustering_visualizer...")
            test_data = {
                'A1': 1, 'A2': 1, 'A3': 1, 'A4': 1, 'A5': 1,
                'A6': 1, 'A7': 1, 'A8': 1, 'A9': 1, 'A10': 0,
                'Age_Mons': 36
            }

            result = clustering_visualizer.generate_clustering_visualization(test_data, k=3)
            if result and result.get('success'):
                print("✅ clustering_visualizer funcionando correctamente")
            else:
                print("⚠️ clustering_visualizer con advertencias, pero continuando...")

            # 2. Verificar que el sistema de clustering de app.py funcione
            print("🔧 Probando sistema de clustering de app.py...")
            if hasattr(clustering_system, 'load_data'):
                data = clustering_system.load_data()
                if data is not None:
                    print(f"✅ Datos cargados: {len(data)} casos")
                else:
                    print("⚠️ Usando datos de ejemplo")

            self.clustering_initialized = True
            print("✅ Sistema de clustering inicializado correctamente")
            return True

        except Exception as e:
            print(f"❌ Error inicializando clustering: {e}")
            print("🔧 Continuando con funcionalidad limitada...")
            return False
    
    def setup_flask_app(self):
        """Configura la aplicación Flask para el modo ejecutable"""
        if EXECUTABLE_MODE:
            # Configurar rutas para archivos estáticos en modo ejecutable
            @app.route('/')
            def index():
                return app.send_static_file('index.html')
            
            @app.route('/<path:filename>')
            def static_files(filename):
                return app.send_static_file(filename)
            
            # Configurar carpeta de archivos estáticos
            app.static_folder = str(BASE_DIR)
            app.static_url_path = ''
        
        # Configurar Flask para producción
        app.config['ENV'] = 'production'
        app.config['DEBUG'] = False
        app.config['TESTING'] = False
        
        return app
    
    def start_server(self):
        """Inicia el servidor Flask en un hilo separado"""
        try:
            self.port = self.find_free_port()
            print(f"🚀 Iniciando servidor en puerto {self.port}")
            
            flask_app = self.setup_flask_app()
            
            # Configurar el servidor para que no muestre logs innecesarios
            import logging
            log = logging.getLogger('werkzeug')
            log.setLevel(logging.ERROR)
            
            self.running = True
            flask_app.run(
                host='127.0.0.1',
                port=self.port,
                debug=False,
                use_reloader=False,
                threaded=True
            )
        except Exception as e:
            print(f"❌ Error iniciando servidor: {e}")
            self.running = False
    
    def wait_for_server(self, timeout=30):
        """Espera a que el servidor esté listo"""
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                response = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                result = response.connect_ex(('127.0.0.1', self.port))
                response.close()
                if result == 0:
                    return True
            except:
                pass
            time.sleep(0.5)
        return False
    
    def open_browser(self):
        """Abre el navegador web"""
        url = f"http://127.0.0.1:{self.port}"
        print(f"🌐 Abriendo navegador en: {url}")
        
        try:
            webbrowser.open(url, new=2)  # new=2 abre en nueva pestaña
            return True
        except Exception as e:
            print(f"❌ Error abriendo navegador: {e}")
            print(f"📋 Abra manualmente: {url}")
            return False
    
    def setup_signal_handlers(self):
        """Configura manejadores de señales para cierre limpio"""
        def signal_handler(signum, frame):
            print("\n🛑 Cerrando aplicación...")
            self.running = False
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def run(self):
        """Ejecuta la aplicación completa"""
        print("🧠 SISTEMA HÍBRIDO DE EVALUACIÓN DE AUTISMO")
        print("=" * 50)
        print("🔧 Versión Ejecutable de Escritorio")
        print("📊 DBSCAN + GMM + ML + Clustering Interactivo")
        print("👶 Edades: 1-16 años (12-192 meses)")
        print("=" * 50)
        
        # Configurar manejadores de señales
        self.setup_signal_handlers()
        
        # Verificar archivos necesarios
        required_files = ['datos.csv', 'index.html', 'styles.css', 'script.js']
        missing_files = []
        
        for file in required_files:
            file_path = BASE_DIR / file
            if not file_path.exists():
                missing_files.append(file)
        
        if missing_files:
            print(f"❌ Archivos faltantes: {missing_files}")
            print("🔧 Asegúrese de que todos los archivos estén en el mismo directorio")
            input("Presione Enter para salir...")
            return
        
        print("✅ Todos los archivos necesarios encontrados")

        # SECUENCIA CORRECTA: 1. Inicializar clustering
        print("\n" + "="*50)
        print("PASO 1: INICIALIZAR SISTEMA DE CLUSTERING")
        print("="*50)
        if not self.initialize_clustering_system():
            print("⚠️ Sistema de clustering con problemas, pero continuando...")

        # SECUENCIA CORRECTA: 2. Iniciar servidor Flask
        print("\n" + "="*50)
        print("PASO 2: INICIAR SERVIDOR FLASK")
        print("="*50)
        print("🚀 Iniciando servidor Flask...")
        self.server_thread = threading.Thread(target=self.start_server, daemon=True)
        self.server_thread.start()
        
        # SECUENCIA CORRECTA: 3. Esperar servidor y abrir navegador
        print("\n" + "="*50)
        print("PASO 3: ABRIR INTERFAZ WEB")
        print("="*50)
        print("⏳ Esperando a que el servidor esté listo...")
        if self.wait_for_server():
            print("✅ Servidor Flask iniciado correctamente")

            # Abrir navegador con index.html
            print("🌐 Abriendo interfaz web (index.html)...")
            if self.open_browser():
                print("✅ Navegador abierto correctamente con index.html")
            
            print("\n" + "=" * 50)
            print("🎉 APLICACIÓN LISTA PARA USAR")
            print("=" * 50)
            print(f"🌐 URL: http://127.0.0.1:{self.port}")
            print("📋 La aplicación se abrió en su navegador web")
            print("🛑 Para cerrar: Cierre esta ventana o presione Ctrl+C")
            print("=" * 50)
            
            # Mantener la aplicación ejecutándose
            try:
                while self.running:
                    time.sleep(1)
            except KeyboardInterrupt:
                print("\n🛑 Cerrando aplicación...")
        else:
            print("❌ Error: El servidor no pudo iniciarse")
            print("🔧 Verifique que el puerto no esté en uso")
            input("Presione Enter para salir...")

def main():
    """Función principal"""
    try:
        app_instance = AutismAssessmentApp()
        app_instance.run()
    except Exception as e:
        print(f"❌ Error crítico: {e}")
        input("Presione Enter para salir...")

if __name__ == "__main__":
    main()
