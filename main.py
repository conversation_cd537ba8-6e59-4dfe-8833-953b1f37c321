#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Sistema Híbrido de Evaluación de Autismo - Ejecutable de Escritorio
Versión ejecutable independiente que combina servidor Flask y navegador web
"""

import os
import sys
import threading
import time
import socket
import webbrowser
import signal
from pathlib import Path

# Configurar el path para el ejecutable
if getattr(sys, 'frozen', False):
    # Ejecutándose como ejecutable empaquetado
    BASE_DIR = Path(sys._MEIPASS)
    EXECUTABLE_MODE = True
else:
    # Ejecutándose como script Python
    BASE_DIR = Path(__file__).parent
    EXECUTABLE_MODE = False

# Agregar el directorio base al path de Python
sys.path.insert(0, str(BASE_DIR))

# Configurar variables de entorno para matplotlib
os.environ['MPLBACKEND'] = 'Agg'

# Importar y verificar módulos en la secuencia correcta
try:
    # 1. Primero importar clustering_visualizer
    print("🔧 Importando sistema de clustering...")
    import clustering_visualizer
    print("✅ Sistema de clustering importado correctamente")

    # 2. Luego importar la aplicación Flask (que usa clustering_visualizer)
    print("🔧 Importando aplicación Flask...")
    from app import app, clustering_system
    print("✅ Aplicación Flask importada correctamente")

except ImportError as e:
    print(f"❌ Error importando módulos: {e}")
    print("🔧 Verifique que todos los archivos estén presentes:")
    print("   • clustering_visualizer.py")
    print("   • app.py")
    print("   • datos.csv")
    sys.exit(1)

class AutismAssessmentApp:
    def __init__(self):
        self.server_thread = None
        self.port = None
        self.running = False
        self.clustering_initialized = False
        
    def find_free_port(self):
        """Encuentra un puerto libre para el servidor, priorizando el puerto 5000"""
        # Intentar primero el puerto 5000 (estándar)
        preferred_port = 5000

        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('127.0.0.1', preferred_port))
                print(f"✅ Puerto {preferred_port} disponible (puerto estándar)")
                return preferred_port
        except OSError:
            print(f"⚠️ Puerto {preferred_port} ocupado, buscando alternativa...")

        # Si 5000 está ocupado, probar puertos cercanos
        alternative_ports = [5001, 5002, 5003, 5004, 5005]

        for port in alternative_ports:
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.bind(('127.0.0.1', port))
                    print(f"✅ Puerto {port} disponible (alternativo)")
                    return port
            except OSError:
                continue

        # Si todos los puertos preferidos están ocupados, usar uno aleatorio
        print("⚠️ Todos los puertos preferidos ocupados, usando puerto aleatorio...")
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(('', 0))
            s.listen(1)
            port = s.getsockname()[1]
            print(f"📍 Puerto aleatorio asignado: {port}")
        return port

    def initialize_clustering_system(self):
        """Inicializa el sistema de clustering siguiendo la secuencia correcta"""
        print("🧠 Inicializando sistema de clustering...")

        try:
            # 1. Verificar que clustering_visualizer funcione
            print("🔧 Probando clustering_visualizer...")
            test_data = {
                'A1': 1, 'A2': 1, 'A3': 1, 'A4': 1, 'A5': 1,
                'A6': 1, 'A7': 1, 'A8': 1, 'A9': 1, 'A10': 0,
                'Age_Mons': 36
            }

            result = clustering_visualizer.generate_clustering_visualization(test_data, k=3)
            if result and result.get('success'):
                print("✅ clustering_visualizer funcionando correctamente")
            else:
                print("⚠️ clustering_visualizer con advertencias, pero continuando...")

            # 2. Verificar que el sistema de clustering de app.py funcione
            print("🔧 Probando sistema de clustering de app.py...")
            if hasattr(clustering_system, 'load_data'):
                data = clustering_system.load_data()
                if data is not None:
                    print(f"✅ Datos cargados: {len(data)} casos")
                else:
                    print("⚠️ Usando datos de ejemplo")

            self.clustering_initialized = True
            print("✅ Sistema de clustering inicializado correctamente")
            return True

        except Exception as e:
            print(f"❌ Error inicializando clustering: {e}")
            print("🔧 Continuando con funcionalidad limitada...")
            return False
    
    def setup_flask_app(self):
        """Configura la aplicación Flask para el modo ejecutable"""
        # Configurar Flask para producción
        app.config['ENV'] = 'production'
        app.config['DEBUG'] = False
        app.config['TESTING'] = False
        app.config['SEND_FILE_MAX_AGE_DEFAULT'] = 0
        app.config['TEMPLATES_AUTO_RELOAD'] = False

        # Las rutas ya están configuradas en app.py, no duplicar aquí
        print(f"✅ Flask configurado para {'ejecutable' if EXECUTABLE_MODE else 'desarrollo'}")

        return app
    
    def start_server(self):
        """Inicia el servidor Flask en un hilo separado con mejor manejo de errores"""
        try:
            self.port = self.find_free_port()
            print(f"🚀 Iniciando servidor Flask en puerto {self.port}")

            flask_app = self.setup_flask_app()

            # Configurar logging para diagnóstico
            import logging
            log = logging.getLogger('werkzeug')
            if EXECUTABLE_MODE:
                log.setLevel(logging.WARNING)  # Mostrar warnings en ejecutable
            else:
                log.setLevel(logging.INFO)

            # Configurar Flask para mejor estabilidad
            flask_app.config['SEND_FILE_MAX_AGE_DEFAULT'] = 0
            flask_app.config['TEMPLATES_AUTO_RELOAD'] = False

            print(f"🔧 Configuración del servidor:")
            print(f"   • Host: 127.0.0.1")
            print(f"   • Puerto: {self.port}")
            print(f"   • Modo ejecutable: {EXECUTABLE_MODE}")
            print(f"   • CORS habilitado: Sí")

            self.running = True

            # Iniciar servidor con configuración optimizada
            flask_app.run(
                host='127.0.0.1',
                port=self.port,
                debug=False,
                use_reloader=False,
                threaded=True,
                use_debugger=False,
                passthrough_errors=False
            )

        except Exception as e:
            print(f"❌ Error crítico iniciando servidor: {e}")
            import traceback
            traceback.print_exc()
            self.running = False
    
    def wait_for_server(self, timeout=30):
        """Espera a que el servidor esté listo y verifica que responda correctamente"""
        start_time = time.time()
        print(f"⏳ Esperando servidor en puerto {self.port}...")

        while time.time() - start_time < timeout:
            try:
                # 1. Verificar que el puerto esté abierto
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                result = sock.connect_ex(('127.0.0.1', self.port))
                sock.close()

                if result == 0:
                    print("✅ Puerto accesible, verificando respuesta HTTP...")

                    # 2. Verificar que el servidor HTTP responda
                    try:
                        import urllib.request
                        url = f"http://127.0.0.1:{self.port}/health"

                        request = urllib.request.Request(url)
                        with urllib.request.urlopen(request, timeout=5) as response:
                            if response.getcode() == 200:
                                print("✅ Servidor Flask respondiendo correctamente")
                                return True
                    except Exception as e:
                        print(f"⚠️ Puerto abierto pero servidor no responde: {e}")

            except Exception as e:
                print(f"🔍 Verificando servidor... ({int(time.time() - start_time)}s)")

            time.sleep(1)

        print(f"❌ Timeout esperando servidor después de {timeout}s")
        return False
    
    def open_browser(self):
        """Abre el navegador web"""
        url = f"http://127.0.0.1:{self.port}"
        print(f"🌐 Abriendo navegador en: {url}")
        
        try:
            webbrowser.open(url, new=2)  # new=2 abre en nueva pestaña
            return True
        except Exception as e:
            print(f"❌ Error abriendo navegador: {e}")
            print(f"📋 Abra manualmente: {url}")
            return False
    
    def verify_server_health(self):
        """Verifica que el servidor esté respondiendo correctamente"""
        try:
            import urllib.request
            url = f"http://127.0.0.1:{self.port}/test-connection"

            request = urllib.request.Request(url)
            with urllib.request.urlopen(request, timeout=3) as response:
                return response.getcode() == 200
        except:
            return False

    def setup_signal_handlers(self):
        """Configura manejadores de señales para cierre limpio"""
        def signal_handler(signum, frame):
            print("\n🛑 Cerrando aplicación...")
            self.running = False
            sys.exit(0)

        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def run(self):
        """Ejecuta la aplicación completa"""
        print("🧠 SISTEMA HÍBRIDO DE EVALUACIÓN DE AUTISMO")
        print("=" * 50)
        print("🔧 Versión Ejecutable de Escritorio")
        print("📊 DBSCAN + GMM + ML + Clustering Interactivo")
        print("👶 Edades: 1-16 años (12-192 meses)")
        print("=" * 50)
        
        # Configurar manejadores de señales
        self.setup_signal_handlers()
        
        # Verificar archivos necesarios
        required_files = ['datos.csv', 'index.html', 'styles.css', 'script.js']
        missing_files = []
        
        for file in required_files:
            file_path = BASE_DIR / file
            if not file_path.exists():
                missing_files.append(file)
        
        if missing_files:
            print(f"❌ Archivos faltantes: {missing_files}")
            print("🔧 Asegúrese de que todos los archivos estén en el mismo directorio")
            input("Presione Enter para salir...")
            return
        
        print("✅ Todos los archivos necesarios encontrados")

        # SECUENCIA CORRECTA: 1. Inicializar clustering
        print("\n" + "="*50)
        print("PASO 1: INICIALIZAR SISTEMA DE CLUSTERING")
        print("="*50)
        if not self.initialize_clustering_system():
            print("⚠️ Sistema de clustering con problemas, pero continuando...")

        # SECUENCIA CORRECTA: 2. Iniciar servidor Flask
        print("\n" + "="*50)
        print("PASO 2: INICIAR SERVIDOR FLASK")
        print("="*50)
        print("🚀 Iniciando servidor Flask...")
        self.server_thread = threading.Thread(target=self.start_server, daemon=True)
        self.server_thread.start()
        
        # SECUENCIA CORRECTA: 3. Esperar servidor y abrir navegador
        print("\n" + "="*50)
        print("PASO 3: ABRIR INTERFAZ WEB")
        print("="*50)
        print("⏳ Esperando a que el servidor esté listo...")
        if self.wait_for_server():
            print("✅ Servidor Flask iniciado correctamente")

            # Abrir navegador con index.html
            print("🌐 Abriendo interfaz web (index.html)...")
            if self.open_browser():
                print("✅ Navegador abierto correctamente con index.html")
            
            print("\n" + "=" * 60)
            print("🎉 APLICACIÓN LISTA PARA USAR")
            print("=" * 60)
            print(f"🌐 URL Principal: http://127.0.0.1:{self.port}")
            print(f"🔍 Health Check: http://127.0.0.1:{self.port}/health")
            print(f"🔗 Test Conexión: http://127.0.0.1:{self.port}/test-connection")
            print("📋 La aplicación se abrió en su navegador web")
            print("")
            print("🔧 INFORMACIÓN DE CONEXIÓN:")
            print(f"   • Servidor Flask: ✅ Activo en puerto {self.port}")
            print(f"   • Host: 127.0.0.1 (localhost)")
            print(f"   • Clustering: ✅ Inicializado")
            print(f"   • CORS: ✅ Configurado para localhost")
            print(f"   • Modo: {'Ejecutable' if EXECUTABLE_MODE else 'Desarrollo'}")
            print("")
            print("📱 ACCESO MANUAL:")
            print(f"   Si el navegador no abre automáticamente,")
            print(f"   copie y pegue esta URL: http://127.0.0.1:{self.port}")
            print("")
            print("🛑 Para cerrar: Cierre esta ventana o presione Ctrl+C")
            print("=" * 60)
            
            # Mantener la aplicación ejecutándose con monitoreo
            try:
                print("\n🔄 Monitoreando conexión...")
                check_count = 0
                while self.running:
                    time.sleep(5)  # Verificar cada 5 segundos
                    check_count += 1

                    # Verificar conexión cada 30 segundos
                    if check_count % 6 == 0:
                        if self.verify_server_health():
                            print(f"✅ Servidor funcionando correctamente ({check_count * 5}s)")
                        else:
                            print(f"⚠️ Problema de conexión detectado ({check_count * 5}s)")

            except KeyboardInterrupt:
                print("\n🛑 Cerrando aplicación...")
        else:
            print("❌ Error: El servidor no pudo iniciarse")
            print("🔧 Posibles soluciones:")
            print("   • Verificar que el puerto no esté en uso")
            print("   • Ejecutar como administrador")
            print("   • Verificar antivirus/firewall")
            print(f"🔍 Para diagnóstico, ejecute: python test_conexion.py")
            input("Presione Enter para salir...")

def main():
    """Función principal"""
    try:
        app_instance = AutismAssessmentApp()
        app_instance.run()
    except Exception as e:
        print(f"❌ Error crítico: {e}")
        input("Presione Enter para salir...")

if __name__ == "__main__":
    main()
