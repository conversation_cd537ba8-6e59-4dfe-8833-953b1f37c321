#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para verificar que las imágenes están incluidas en el ejecutable
"""

import os
import time
import threading
import webbrowser
import urllib.request
from main import AutismAssessmentApp

def test_ejecutable_con_imagenes():
    """Prueba completa del ejecutable con verificación de imágenes"""
    print("🔍 PROBANDO EJECUTABLE CON IMÁGENES")
    print("=" * 60)
    
    try:
        app = AutismAssessmentApp()
        
        # Iniciar servidor en hilo separado
        def run_server():
            try:
                app.run()
            except:
                pass
        
        server_thread = threading.Thread(target=run_server, daemon=True)
        server_thread.start()
        
        # Esperar a que el servidor inicie
        print("⏳ Esperando servidor...")
        time.sleep(5)
        
        if hasattr(app, 'port') and app.port:
            print(f"📍 Servidor iniciado en puerto: {app.port}")
            
            # Lista de imágenes a verificar
            imagenes_a_verificar = [
                '01_eda_distribuciones_numericas.png',
                '02_eda_variables_categoricas.png',
                '03_eda_matriz_correlacion.png',
                '04_eda_analisis_grupos_riesgo.png',
                '05_clustering_inicial_pca.png',
                '06_clustering_inicial_tsne.png',
                '07_comparacion_antes_despues.png',
                '08_importancia_caracteristicas.png',
                '09_arboles_decision_comparacion.png',
                '10_analisis_clusters_final.png'
            ]
            
            imagenes_ok = 0
            imagenes_total = len(imagenes_a_verificar)
            
            print(f"\n🖼️ VERIFICANDO {imagenes_total} IMÁGENES:")
            print("-" * 50)
            
            for i, imagen in enumerate(imagenes_a_verificar, 1):
                try:
                    url_imagen = f"http://127.0.0.1:{app.port}/PaginaGraficos/graficos_generados/{imagen}"
                    request = urllib.request.Request(url_imagen)
                    
                    with urllib.request.urlopen(request, timeout=5) as response:
                        if response.getcode() == 200:
                            content_type = response.headers.get('Content-Type', '')
                            content_length = response.headers.get('Content-Length', '0')
                            
                            if content_type.startswith('image/'):
                                size_kb = int(content_length) // 1024 if content_length.isdigit() else 0
                                print(f"  ✅ {i:2d}. {imagen} ({size_kb} KB)")
                                imagenes_ok += 1
                            else:
                                print(f"  ❌ {i:2d}. {imagen} - Tipo incorrecto: {content_type}")
                        else:
                            print(f"  ❌ {i:2d}. {imagen} - Error HTTP: {response.getcode()}")
                except Exception as e:
                    print(f"  ❌ {i:2d}. {imagen} - Error: {str(e)[:50]}...")
            
            print("-" * 50)
            print(f"📊 RESULTADO: {imagenes_ok}/{imagenes_total} imágenes cargadas correctamente")
            
            if imagenes_ok == imagenes_total:
                print("🎉 ¡TODAS LAS IMÁGENES FUNCIONAN CORRECTAMENTE!")
                
                # Probar página de gráficos completa
                print("\n🌐 PROBANDO PÁGINA DE GRÁFICOS COMPLETA:")
                try:
                    url_graficos = f"http://127.0.0.1:{app.port}/graficos"
                    request = urllib.request.Request(url_graficos)
                    
                    with urllib.request.urlopen(request, timeout=5) as response:
                        if response.getcode() == 200:
                            content = response.read().decode('utf-8')
                            
                            # Verificar contenido esperado
                            checks = [
                                ('Análisis de Clustering', 'Título principal'),
                                ('Visualizaciones Generadas', 'Sección de gráficos'),
                                ('graficos_generados/', 'Referencias a imágenes'),
                                ('10 gráficos', 'Contador de gráficos')
                            ]
                            
                            checks_ok = 0
                            for check_text, description in checks:
                                if check_text in content:
                                    print(f"  ✅ {description}")
                                    checks_ok += 1
                                else:
                                    print(f"  ❌ {description}")
                            
                            if checks_ok == len(checks):
                                print("✅ Página de gráficos completamente funcional")
                                
                                # Abrir navegador para verificación manual
                                print(f"\n🌐 Abriendo navegador en: http://127.0.0.1:{app.port}")
                                webbrowser.open(f"http://127.0.0.1:{app.port}", new=2)
                                
                                print("\n📋 VERIFICACIÓN MANUAL:")
                                print("1. La página principal debe cargar correctamente")
                                print("2. Haga clic en 'Gráficos' en la navegación")
                                print("3. Debe abrir nueva pestaña con página de gráficos")
                                print("4. TODAS las 10 imágenes deben cargar correctamente")
                                print("5. No debe haber iconos de imagen rota")
                                print("6. Las imágenes deben mostrar gráficos de análisis")
                                
                                # Mantener servidor activo
                                print(f"\n🔄 Servidor activo por 45 segundos para verificación...")
                                for i in range(45):
                                    print(f"⏱️ {45-i} segundos restantes...", end='\r')
                                    time.sleep(1)
                                
                                print("\n✅ Verificación completada")
                                return True
                            else:
                                print("⚠️ Página de gráficos con contenido incompleto")
                                return False
                        else:
                            print(f"❌ Error cargando página de gráficos: {response.getcode()}")
                            return False
                except Exception as e:
                    print(f"❌ Error accediendo página de gráficos: {e}")
                    return False
            else:
                print("❌ ALGUNAS IMÁGENES NO FUNCIONAN CORRECTAMENTE")
                print("🔧 Posibles causas:")
                print("   • Las imágenes no se incluyeron en el ejecutable")
                print("   • Error en las rutas de archivos estáticos")
                print("   • Problema con el servidor Flask")
                return False
        else:
            print("❌ Servidor no pudo iniciar")
            return False
            
    except Exception as e:
        print(f"❌ Error en prueba: {e}")
        return False

def verificar_archivos_spec():
    """Verifica el archivo .spec generado"""
    print("\n🔍 VERIFICANDO ARCHIVO .SPEC")
    print("=" * 40)
    
    spec_file = 'SistemaAutismo_con_imagenes.spec'
    
    if os.path.exists(spec_file):
        print(f"✅ Archivo encontrado: {spec_file}")
        
        try:
            with open(spec_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Contar referencias a imágenes PNG
            png_count = content.count('.png')
            print(f"📊 Referencias a PNG en .spec: {png_count}")
            
            if png_count >= 10:
                print("✅ Todas las imágenes están referenciadas")
                return True
            else:
                print("⚠️ Posiblemente faltan referencias a imágenes")
                return False
                
        except Exception as e:
            print(f"❌ Error leyendo .spec: {e}")
            return False
    else:
        print(f"❌ Archivo .spec no encontrado: {spec_file}")
        return False

def main():
    """Función principal"""
    print("🔧 VERIFICACIÓN DE IMÁGENES EN EJECUTABLE")
    print("🧠 Sistema de Evaluación de Autismo")
    print("🖼️ Prueba de Integración de Gráficos")
    print("=" * 70)
    
    # Verificar archivo .spec
    spec_ok = verificar_archivos_spec()
    
    if spec_ok:
        print("\n🚀 INICIANDO PRUEBA COMPLETA DEL EJECUTABLE...")
        
        if test_ejecutable_con_imagenes():
            print("\n" + "=" * 70)
            print("🎉 ¡ÉXITO TOTAL!")
            print("=" * 70)
            print("✅ Todas las imágenes están incluidas y funcionan")
            print("✅ La página de gráficos carga correctamente")
            print("✅ El ejecutable está completamente funcional")
            print("🔗 El botón 'Gráficos' muestra las 10 visualizaciones")
            print("=" * 70)
        else:
            print("\n" + "=" * 70)
            print("❌ PROBLEMAS DETECTADOS")
            print("=" * 70)
            print("⚠️ Algunas imágenes no funcionan correctamente")
            print("🔧 Revise los errores mostrados arriba")
            print("=" * 70)
    else:
        print("\n❌ Problemas con el archivo .spec")
        print("🔧 Ejecute nuevamente build_exe_con_imagenes.py")
    
    input("\nPresione Enter para salir...")

if __name__ == "__main__":
    main()
