#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para construir el ejecutable del Sistema de Evaluación de Autismo
Utiliza PyInstaller para crear un ejecutable independiente
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def check_requirements():
    """Verifica que PyInstaller esté instalado"""
    try:
        import PyInstaller
        print("✅ PyInstaller encontrado")
        return True
    except ImportError:
        print("❌ PyInstaller no encontrado")
        print("🔧 Instalando PyInstaller...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
            print("✅ PyInstaller instalado correctamente")
            return True
        except subprocess.CalledProcessError:
            print("❌ Error instalando PyInstaller")
            return False

def create_spec_file():
    """Crea el archivo .spec para PyInstaller"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# Archivos de datos a incluir
datas = [
    ('datos.csv', '.'),
    ('index.html', '.'),
    ('styles.css', '.'),
    ('script.js', '.'),
    ('clustering_ui.js', '.'),
    ('PaginaGraficos', 'PaginaGraficos'),
]

# Módulos ocultos necesarios
hiddenimports = [
    'sklearn.utils._cython_blas',
    'sklearn.neighbors.typedefs',
    'sklearn.neighbors.quad_tree',
    'sklearn.tree._utils',
    'sklearn.cluster._dbscan_inner',
    'sklearn.cluster._k_means_fast',
    'sklearn.cluster._k_means_elkan',
    'sklearn.cluster._k_means_lloyd',
    'sklearn.utils._heap',
    'sklearn.utils._sorting',
    'sklearn.utils._vector_sentinel',
    'matplotlib.backends.backend_agg',
    'matplotlib.backends.backend_pdf',
    'matplotlib.backends.backend_ps',
    'matplotlib.backends.backend_svg',
    'matplotlib.figure',
    'matplotlib.pyplot',
    'pandas._libs.tslibs.timedeltas',
    'pandas._libs.tslibs.np_datetime',
    'pandas._libs.tslibs.nattype',
    'pandas._libs.skiplist',
    'numpy.random.common',
    'numpy.random.bounded_integers',
    'numpy.random.entropy',
]

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='SistemaAutismo',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='brain_icon.ico' if os.path.exists('brain_icon.ico') else None,
)
'''
    
    with open('SistemaAutismo.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ Archivo .spec creado")

def check_files():
    """Verifica que todos los archivos necesarios estén presentes"""
    required_files = [
        'main.py',
        'app.py', 
        'clustering_visualizer.py',
        'datos.csv',
        'index.html',
        'styles.css',
        'script.js',
        'clustering_ui.js'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ Archivos faltantes: {missing_files}")
        return False
    
    print("✅ Todos los archivos necesarios encontrados")
    return True

def build_executable():
    """Construye el ejecutable usando PyInstaller"""
    print("🔨 Construyendo ejecutable...")
    
    try:
        # Limpiar builds anteriores
        if os.path.exists('build'):
            shutil.rmtree('build')
        if os.path.exists('dist'):
            shutil.rmtree('dist')
        
        # Ejecutar PyInstaller con configuración optimizada
        cmd = [
            'pyinstaller',
            '--onefile',
            '--name=SistemaAutismo',
            '--add-data=datos.csv;.',
            '--add-data=index.html;.',
            '--add-data=styles.css;.',
            '--add-data=script.js;.',
            '--add-data=clustering_ui.js;.',
            '--add-data=PaginaGraficos;PaginaGraficos',
            # Imports ocultos para sklearn
            '--hidden-import=sklearn.utils._cython_blas',
            '--hidden-import=sklearn.neighbors.typedefs',
            '--hidden-import=sklearn.neighbors.quad_tree',
            '--hidden-import=sklearn.tree._utils',
            '--hidden-import=sklearn.cluster._dbscan_inner',
            '--hidden-import=sklearn.cluster._k_means_fast',
            '--hidden-import=sklearn.utils._heap',
            '--hidden-import=sklearn.utils._sorting',
            '--hidden-import=sklearn.utils._vector_sentinel',
            # Imports ocultos para matplotlib
            '--hidden-import=matplotlib.backends.backend_agg',
            '--hidden-import=matplotlib.backends.backend_pdf',
            '--hidden-import=matplotlib.backends.backend_ps',
            '--hidden-import=matplotlib.backends.backend_svg',
            '--hidden-import=matplotlib.figure',
            '--hidden-import=matplotlib.pyplot',
            # Imports ocultos para pandas
            '--hidden-import=pandas._libs.tslibs.timedeltas',
            '--hidden-import=pandas._libs.tslibs.np_datetime',
            '--hidden-import=pandas._libs.tslibs.nattype',
            '--hidden-import=pandas._libs.skiplist',
            # Imports ocultos para numpy
            '--hidden-import=numpy.random.common',
            '--hidden-import=numpy.random.bounded_integers',
            '--hidden-import=numpy.random.entropy',
            # Configuración de ventana
            '--console',
            '--noconfirm',
            'main.py'
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Ejecutable construido exitosamente")
            print("📁 Ubicación: dist/SistemaAutismo.exe")
            return True
        else:
            print("❌ Error construyendo ejecutable:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Error durante la construcción: {e}")
        return False

def create_readme():
    """Crea un archivo README para el ejecutable"""
    readme_content = """# Sistema Híbrido de Evaluación de Autismo - Ejecutable

## Descripción
Aplicación de escritorio independiente para evaluación de autismo en niños de 1-16 años.
Utiliza algoritmos avanzados de machine learning (DBSCAN + GMM + ML + Clustering).

## Uso
1. Ejecute `SistemaAutismo.exe`
2. La aplicación abrirá automáticamente su navegador web
3. Complete el cuestionario de 10 preguntas
4. Obtenga resultados con visualizaciones interactivas

## Características
- ✅ No requiere instalación de Python
- ✅ Ejecutable independiente
- ✅ Interfaz web moderna
- ✅ Análisis con clustering interactivo
- ✅ Visualizaciones avanzadas
- ✅ Recomendaciones personalizadas

## Requisitos del Sistema
- Windows 10 o superior
- 4 GB de RAM mínimo
- 500 MB de espacio libre
- Navegador web moderno

## Soporte
Para soporte técnico o preguntas, consulte la documentación incluida.

## Importante
Esta herramienta es para fines informativos y de detección temprana.
No reemplaza el diagnóstico profesional.
Siempre consulte con un especialista en desarrollo infantil.
"""
    
    with open('README_EJECUTABLE.txt', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✅ README creado")

    # Copiar script de diagnóstico
    if os.path.exists('test_conexion.py'):
        import shutil
        shutil.copy2('test_conexion.py', 'dist/')
        print("✅ Script de diagnóstico copiado a dist/")

def main():
    """Función principal del script de construcción"""
    print("🧠 CONSTRUCTOR DE EJECUTABLE - SISTEMA DE EVALUACIÓN DE AUTISMO")
    print("=" * 60)
    
    # Verificar requisitos
    if not check_requirements():
        return
    
    # Verificar archivos
    if not check_files():
        return
    
    # Crear archivo spec
    create_spec_file()
    
    # Construir ejecutable
    if build_executable():
        create_readme()
        print("\n" + "=" * 60)
        print("🎉 CONSTRUCCIÓN COMPLETADA EXITOSAMENTE")
        print("=" * 60)
        print("📁 Ejecutable: dist/SistemaAutismo.exe")
        print("📋 README: README_EJECUTABLE.txt")
        print("🚀 El ejecutable está listo para distribuir")
        print("=" * 60)
    else:
        print("\n❌ Error en la construcción del ejecutable")

if __name__ == "__main__":
    main()
