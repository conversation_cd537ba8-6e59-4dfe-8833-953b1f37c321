#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test básico que solo verifica lo esencial sin ejecutar nada complejo
"""

import os
import sys

def main():
    print("🔧 TEST BÁSICO - VERIFICACIÓN ESENCIAL")
    print("=" * 50)
    
    # 1. Verificar archivos
    print("\n1. ARCHIVOS NECESARIOS:")
    archivos = ['app.py', 'main.py', 'index.html', 'script.js', 'styles.css', 'datos.csv']
    archivos_ok = 0
    
    for archivo in archivos:
        if os.path.exists(archivo):
            print(f"   ✅ {archivo}")
            archivos_ok += 1
        else:
            print(f"   ❌ {archivo} - FALTANTE")
    
    print(f"   📊 Archivos: {archivos_ok}/{len(archivos)}")
    
    # 2. Verificar corrección en app.py
    print("\n2. CORRECCIONES EN APP.PY:")
    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        correcciones = [
            ('self.data = None', 'Inicialización de data'),
            ('hasattr(clustering_system, \'data\')', 'Verificación hasattr'),
            ('test-connection', 'Endpoint test-connection'),
            ('BasicClusteringSystem', 'Sistema de respaldo')
        ]
        
        correcciones_ok = 0
        for buscar, descripcion in correcciones:
            if buscar in content:
                print(f"   ✅ {descripcion}")
                correcciones_ok += 1
            else:
                print(f"   ❌ {descripcion}")
        
        print(f"   📊 Correcciones: {correcciones_ok}/{len(correcciones)}")
        
    except Exception as e:
        print(f"   ❌ Error leyendo app.py: {e}")
        correcciones_ok = 0
    
    # 3. Verificar corrección en script.js
    print("\n3. CORRECCIONES EN SCRIPT.JS:")
    try:
        with open('script.js', 'r', encoding='utf-8') as f:
            content = f.read()
        
        js_correcciones = [
            ('detectServerPort', 'Detección automática de puerto'),
            ('API_BASE_URL', 'Variable de URL base'),
            ('checkBackendConnection', 'Verificación de conexión')
        ]
        
        js_correcciones_ok = 0
        for buscar, descripcion in js_correcciones:
            if buscar in content:
                print(f"   ✅ {descripcion}")
                js_correcciones_ok += 1
            else:
                print(f"   ❌ {descripcion}")
        
        print(f"   📊 Correcciones JS: {js_correcciones_ok}/{len(js_correcciones)}")
        
    except Exception as e:
        print(f"   ❌ Error leyendo script.js: {e}")
        js_correcciones_ok = 0
    
    # 4. Verificar sintaxis básica
    print("\n4. VERIFICACIÓN DE SINTAXIS:")
    sintaxis_ok = 0
    
    for archivo in ['app.py', 'main.py']:
        if os.path.exists(archivo):
            try:
                with open(archivo, 'r', encoding='utf-8') as f:
                    content = f.read()
                compile(content, archivo, 'exec')
                print(f"   ✅ {archivo} - Sintaxis correcta")
                sintaxis_ok += 1
            except SyntaxError as e:
                print(f"   ❌ {archivo} - Error de sintaxis: {e}")
            except Exception as e:
                print(f"   ❌ {archivo} - Error: {e}")
        else:
            print(f"   ❌ {archivo} - No encontrado")
    
    print(f"   📊 Sintaxis: {sintaxis_ok}/2")
    
    # 5. Resumen final
    print("\n" + "=" * 50)
    print("📊 RESUMEN FINAL")
    print("=" * 50)
    
    total_puntos = archivos_ok + correcciones_ok + js_correcciones_ok + sintaxis_ok
    max_puntos = len(archivos) + len(correcciones) + len(js_correcciones) + 2
    
    porcentaje = (total_puntos / max_puntos) * 100
    
    print(f"📈 Puntuación total: {total_puntos}/{max_puntos} ({porcentaje:.1f}%)")
    
    if porcentaje >= 90:
        print("🎉 EXCELENTE - Todo listo para reconstruir")
        estado = "EXCELENTE"
    elif porcentaje >= 75:
        print("✅ BUENO - Debería funcionar correctamente")
        estado = "BUENO"
    elif porcentaje >= 50:
        print("⚠️ REGULAR - Puede funcionar con problemas menores")
        estado = "REGULAR"
    else:
        print("❌ MALO - Necesita correcciones antes de continuar")
        estado = "MALO"
    
    print("\n🔧 RECOMENDACIONES:")
    if estado == "EXCELENTE" or estado == "BUENO":
        print("✅ Puede proceder a reconstruir el ejecutable")
        print("🚀 Ejecute: python rebuild_exe_corregido.py")
    elif estado == "REGULAR":
        print("⚠️ Revise los elementos faltantes mostrados arriba")
        print("🔧 Corrija los problemas y vuelva a ejecutar este test")
    else:
        print("❌ Debe corregir los problemas críticos primero")
        print("📋 Verifique que todos los archivos estén presentes")
        print("🔧 Corrija errores de sintaxis")
    
    print("\n" + "=" * 50)
    
    return estado

if __name__ == "__main__":
    resultado = main()
    print(f"\nEstado final: {resultado}")
    input("Presione Enter para salir...")
