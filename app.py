from flask import Flask, request, jsonify, send_from_directory
from flask_cors import CORS
import pandas as pd
import numpy as np
import sys
import os
from pathlib import Path

# Configurar paths para ejecutable
if getattr(sys, 'frozen', False):
    # Ejecutándose como ejecutable empaquetado
    BASE_DIR = Path(sys._MEIPASS)
    EXECUTABLE_MODE = True
else:
    # Ejecutándose como script Python
    BASE_DIR = Path(__file__).parent
    EXECUTABLE_MODE = False

# Configurar matplotlib para usar backend no interactivo ANTES de importar pyplot
import matplotlib
matplotlib.use('Agg')  # Backend no interactivo para aplicaciones web
import matplotlib.pyplot as plt
import seaborn as sns

from sklearn.cluster import DBSCAN
from sklearn.preprocessing import RobustScaler
from sklearn.decomposition import PCA
from sklearn.metrics import silhouette_score
from sklearn.neighbors import NearestNeighbors
import json
import base64
from io import BytesIO
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Configuración adicional para evitar problemas de threading
import threading
matplotlib.rcParams['figure.max_open_warning'] = 0

# Importar la nueva funcionalidad de clustering
from clustering_visualizer import generate_clustering_visualization

# Configurar Flask
if EXECUTABLE_MODE:
    app = Flask(__name__, static_folder=str(BASE_DIR), static_url_path='')
else:
    app = Flask(__name__)

# Configuración de CORS más específica para el ejecutable
if EXECUTABLE_MODE:
    # En modo ejecutable, permitir conexiones desde localhost
    CORS(app, resources={
        r"/*": {
            "origins": ["http://127.0.0.1:*", "http://localhost:*"],
            "methods": ["GET", "POST", "OPTIONS"],
            "allow_headers": ["Content-Type", "Authorization"]
        }
    })
else:
    # En modo desarrollo, CORS más permisivo
    CORS(app)

print("🧠 SISTEMA HÍBRIDO COMPLETO DE EVALUACIÓN DE AUTISMO CON CLUSTERING")
print("=" * 70)
print("🔧 Versión: Completa - Sistema con Visualizaciones Avanzadas + Combobox K")
print("📊 Rango de edad: 1-16 años (12-192 meses)")
print("🌐 Puerto: Dinámico")
print("✅ CORS habilitado")
print("📈 Clustering con DBSCAN y visualizaciones matplotlib avanzadas")
if EXECUTABLE_MODE:
    print("🎯 Modo: Ejecutable de escritorio")
else:
    print("🎯 Modo: Desarrollo")

def convert_numpy_types(obj):
    """Convierte tipos de NumPy a tipos nativos de Python para serialización JSON"""
    if isinstance(obj, np.integer):
        return int(obj)
    elif isinstance(obj, np.floating):
        return float(obj)
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, dict):
        return {key: convert_numpy_types(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [convert_numpy_types(item) for item in obj]
    else:
        return obj

class AutismClusteringAnalysis:
    def __init__(self, csv_path=None):
        """Sistema completo de análisis de clustering para autismo"""
        if csv_path is None:
            if EXECUTABLE_MODE:
                # En modo ejecutable, buscar en el directorio base
                possible_paths = [
                    BASE_DIR / 'datos.csv',
                    Path('datos.csv'),
                ]
            else:
                # En modo desarrollo, buscar en ubicaciones típicas
                possible_paths = [
                    Path('datos.csv'),
                    Path('./datos.csv'),
                    Path('../datos.csv'),
                    Path('data/datos.csv'),
                    Path(__file__).parent / 'datos.csv'
                ]

            for path in possible_paths:
                if path.exists():
                    csv_path = str(path)
                    break
        
        self.csv_path = csv_path
        self.scaler = RobustScaler()
        self.dbscan_models = {}  # Para diferentes configuraciones de DBSCAN
        self.pca_model = None
        self.cluster_profiles = {}
        self.historical_data = None
        self.processed_data = None
        self.elbow_data = None
        self.data = None  # Inicializar el atributo data

        print(f"🔍 Buscando archivo CSV en: {self.csv_path}")

        # Cargar datos inmediatamente al inicializar
        try:
            self.data = self.load_data()
            if self.data is not None:
                print(f"✅ Datos cargados en inicialización: {len(self.data)} casos")
            else:
                print("⚠️ No se pudieron cargar datos en inicialización")
        except Exception as e:
            print(f"❌ Error cargando datos en inicialización: {e}")
            self.data = None
        
    def load_data(self):
        """Carga los datos del archivo CSV o genera datos de ejemplo"""
        try:
            if self.csv_path and os.path.exists(self.csv_path):
                print(f"📂 Cargando datos desde: {self.csv_path}")
                
                # Intentar diferentes encodings y separadores
                encodings = ['utf-8', 'latin-1', 'iso-8859-1', 'cp1252']
                separators = [',', ';', '\t']
                
                for encoding in encodings:
                    for sep in separators:
                        try:
                            df = pd.read_csv(self.csv_path, encoding=encoding, sep=sep)
                            if len(df.columns) > 1 and len(df) > 0:
                                print(f"✅ Datos cargados exitosamente: {len(df)} filas, {len(df.columns)} columnas")
                                
                                # Procesar columnas si están en formato combinado
                                if len(df.columns) == 1:
                                    # Separar la primera columna si contiene todos los datos
                                    first_col = df.columns[0]
                                    if ';' in first_col:
                                        # Usar el header como nombres de columnas
                                        col_names = first_col.split(';')
                                        # Recrear el DataFrame
                                        data_rows = []
                                        for _, row in df.iterrows():
                                            values = str(row.iloc[0]).split(';')
                                            data_rows.append(values)
                                        df = pd.DataFrame(data_rows, columns=col_names)
                                        print(f"📊 Datos reestructurados: {len(df)} filas, {len(df.columns)} columnas")
                                
                                print(f"📊 Columnas encontradas: {list(df.columns)}")
                                self.historical_data = df
                                self.data = df  # Actualizar el atributo data
                                return df
                        except Exception as e:
                            continue
            
            # Si no se puede cargar, generar datos de ejemplo
            print("⚠️ No se pudo cargar el archivo CSV, generando datos de ejemplo...")
            return self.generate_sample_data()
            
        except Exception as e:
            print(f"❌ Error cargando datos: {e}")
            return self.generate_sample_data()
    
    def generate_sample_data(self):
        """Genera datos de ejemplo optimizados para clustering"""
        np.random.seed(42)
        n_samples = 1200
        
        # Crear grupos de riesgo bien definidos para DBSCAN
        risk_groups = [
            {
                'name': 'Bajo_Riesgo', 
                'size': 500, 
                'A1_A9_prob': 0.85, 
                'A10_prob': 0.15, 
                'age_range': (24, 84), 
                'center': [0.2, 0.2],
                'spread': 0.3
            },
            {
                'name': 'Riesgo_Moderado', 
                'size': 400, 
                'A1_A9_prob': 0.60, 
                'A10_prob': 0.40, 
                'age_range': (18, 72), 
                'center': [0.5, 0.5],
                'spread': 0.25
            },
            {
                'name': 'Alto_Riesgo', 
                'size': 250, 
                'A1_A9_prob': 0.25, 
                'A10_prob': 0.75, 
                'age_range': (12, 60), 
                'center': [0.8, 0.8],
                'spread': 0.2
            },
            {
                'name': 'Casos_Atipicos', 
                'size': 50, 
                'A1_A9_prob': 0.50, 
                'A10_prob': 0.50, 
                'age_range': (12, 96), 
                'center': [0.3, 0.7],
                'spread': 0.4
            }
        ]
        
        data = []
        case_id = 1
        
        for group in risk_groups:
            for i in range(group['size']):
                sample = {'Case_No': case_id}
                
                # Para bajo riesgo: A1-A9 tienen alta probabilidad de ser 1 (respuestas típicas)
                # A10 tiene baja probabilidad de ser 1
                for j in range(1, 10):
                    sample[f'A{j}'] = 1 if np.random.random() < group['A1_A9_prob'] else 0
                sample['A10'] = 1 if np.random.random() < group['A10_prob'] else 0

                # Score corregido
                score = sum(1 - sample[f'A{k}'] for k in range(1, 10)) + sample['A10']
                sample['Qchat-10-Score'] = score
                
                # Edad
                age_min, age_max = group['age_range']
                age_mean = (age_min + age_max) / 2
                age_std = (age_max - age_min) / 6
                age = np.random.normal(age_mean, age_std)
                sample['Age_Mons'] = int(np.clip(age, age_min, age_max))
                
                # Score corregido
                score = sum(1 - sample[f'A{k}'] for k in range(1, 10)) + sample['A10']
                sample['Qchat-10-Score'] = score
                
                # Variables adicionales
                sample['Sex'] = np.random.choice(['m', 'f'])
                sample['True_Risk_Level'] = group['name']
                
                # Etiqueta ASD basada en el grupo
                if group['name'] == 'Alto_Riesgo':
                    sample['Class/ASD'] = 'YES' if np.random.random() < 0.80 else 'NO'
                elif group['name'] == 'Riesgo_Moderado':
                    sample['Class/ASD'] = 'YES' if np.random.random() < 0.35 else 'NO'
                elif group['name'] == 'Casos_Atipicos':
                    sample['Class/ASD'] = 'YES' if np.random.random() < 0.60 else 'NO'
                else:  # Bajo_Riesgo
                    sample['Class/ASD'] = 'YES' if np.random.random() < 0.08 else 'NO'
                
                data.append(sample)
                case_id += 1
        
        df = pd.DataFrame(data)
        self.historical_data = df
        self.data = df  # Actualizar el atributo data
        print(f"✅ Datos de ejemplo generados: {len(df)} casos con grupos de riesgo definidos")
        
        # Mostrar distribución de grupos
        print("📊 Distribución de grupos:")
        for group_name in df['True_Risk_Level'].unique():
            count = len(df[df['True_Risk_Level'] == group_name])
            asd_rate = (df[df['True_Risk_Level'] == group_name]['Class/ASD'] == 'YES').mean() * 100
            print(f"   • {group_name}: {count} casos ({asd_rate:.1f}% ASD)")
        
        return df

    def run_complete_analysis(self, user_responses, selected_k=3):
        """
        Ejecuta el análisis completo de clustering con DBSCAN
        selected_k se usa para determinar la configuración de DBSCAN
        """
        print(f"🧠 Iniciando análisis completo con DBSCAN (configuración {selected_k})...")
        
        try:
            # 1. Cargar datos
            df = self.load_data()
            if df is None:
                return {'success': False, 'error': 'No se pudieron cargar los datos'}
            
            # 2. Preprocesar datos
            processed_data = self.preprocess_data(df)
            if processed_data is None:
                return {'success': False, 'error': 'Error en el preprocesamiento'}
            
            # 3. Entrenar modelos DBSCAN
            models = self.train_dbscan_models()
            if models is None:
                return {'success': False, 'error': 'Error entrenando modelos DBSCAN'}
            
            # 4. Predecir cluster del usuario
            user_prediction = self.predict_user_cluster(user_responses, selected_k)
            if user_prediction is None:
                return {'success': False, 'error': 'Error en la predicción del usuario'}
            
            # 5. Crear reporte del cluster
            cluster_report = self.create_cluster_report(user_prediction, selected_k)
            
            # 6. Crear visualización
            visualization = self.create_clustering_visualization(user_prediction, selected_k)
            
            return {
                'success': True,
                'cluster_report': cluster_report,
                'clustering_plot': visualization,
                'selected_k': selected_k,
                'optimal_k_suggested': 3,
                'total_cases': len(df),
                'data_source': 'Datos de ejemplo' if self.csv_path is None else self.csv_path,
                'selected_algorithm': 'DBSCAN'
            }
            
        except Exception as e:
            print(f"❌ Error en análisis completo: {e}")
            import traceback
            traceback.print_exc()
            return {'success': False, 'error': f'Error en análisis: {str(e)}'}

    def preprocess_data(self, df):
        """
        Preprocesa los datos para clustering
        """
        print("🔧 Preprocesando datos para clustering...")
        
        # Seleccionar características para clustering
        feature_cols = [f'A{i}' for i in range(1, 11)] + ['Age_Mons']
        
        # Verificar que las columnas existen
        available_cols = [col for col in feature_cols if col in df.columns]
        if not available_cols:
            print("❌ No se encontraron columnas de características esperadas")
            return None
        
        print(f"📊 Características para clustering: {available_cols}")
        
        # Extraer matriz de características
        X = df[available_cols].values
        
        # Escalado robusto
        X_scaled = self.scaler.fit_transform(X)
        
        # PCA para visualización (2D)
        self.pca_model = PCA(n_components=2, random_state=42)
        X_pca = self.pca_model.fit_transform(X_scaled)
        
        self.processed_data = {
            'X_scaled': X_scaled,
            'X_pca': X_pca,
            'feature_names': available_cols,
            'original_data': df
        }
        
        print(f"✅ Preprocesamiento completado: {X_scaled.shape}")
        return self.processed_data
    
    def find_optimal_eps(self, X, k=5):
        """
        Encuentra el valor óptimo de eps para DBSCAN usando el método del codo
        """
        neigh = NearestNeighbors(n_neighbors=k)
        neigh.fit(X)
        distances, _ = neigh.kneighbors(X)
        distances = np.sort(distances[:, k-1])
        
        # Calcular la pendiente
        slopes = np.diff(distances)
        
        # Encontrar el punto de inflexión (método del codo)
        knee_point = np.argmax(slopes) + 1
        eps = distances[knee_point]
        
        # Ajustar eps para mejorar la detección de clusters
        return eps
    
    def train_dbscan_models(self):
        """
        Entrena modelos DBSCAN para diferentes configuraciones
        """
        if not self.processed_data:
            print("❌ Datos no procesados")
            return None
        
        X = self.processed_data['X_scaled']
        
        print("🎯 Entrenando modelos DBSCAN para diferentes configuraciones...")
        
        # Encontrar eps óptimo
        eps_optimal = self.find_optimal_eps(X)
        print(f"📏 Eps óptimo calculado: {eps_optimal:.4f}")
        
        # Configuración para 2 clusters principales
        dbscan_2 = DBSCAN(eps=eps_optimal*0.9, min_samples=3)
        dbscan_2.fit(X)
        labels_2 = dbscan_2.labels_

        # Verificar número de clusters (excluyendo ruido)
        n_clusters_2 = len(set(labels_2)) - (1 if -1 in labels_2 else 0)
        print(f"🔍 DBSCAN (config 2) encontró {n_clusters_2} clusters y {np.sum(labels_2 == -1)} puntos de ruido")

        # Configuración para 3 clusters principales - usar DBSCAN con parámetros ajustados
        # Probar diferentes valores de eps para obtener exactamente 3 clusters
        min_samples_3 = 3  # Usar el mismo min_samples que K=2
        best_eps_3 = eps_optimal
        best_labels_3 = None
        best_n_clusters_3 = 0

        # Probar valores de eps más pequeños para obtener más clusters
        eps_values = [eps_optimal * 0.3, eps_optimal * 0.4, eps_optimal * 0.5, eps_optimal * 0.6, eps_optimal * 0.7]

        for test_eps in eps_values:
            dbscan_test = DBSCAN(eps=test_eps, min_samples=min_samples_3)
            test_labels = dbscan_test.fit_predict(X)
            test_n_clusters = len(set(test_labels)) - (1 if -1 in test_labels else 0)

            if test_n_clusters == 3:
                best_eps_3 = test_eps
                best_labels_3 = test_labels
                best_n_clusters_3 = test_n_clusters
                break
            elif test_n_clusters > 3 and best_n_clusters_3 < 3:
                # Si no encontramos exactamente 3, usar el que más se acerque
                best_eps_3 = test_eps
                best_labels_3 = test_labels
                best_n_clusters_3 = test_n_clusters

        # Si no encontramos 3 clusters, usar el mejor resultado
        if best_labels_3 is None:
            best_eps_3 = eps_optimal * 0.5
            dbscan_3 = DBSCAN(eps=best_eps_3, min_samples=min_samples_3)
            best_labels_3 = dbscan_3.fit_predict(X)
            best_n_clusters_3 = len(set(best_labels_3)) - (1 if -1 in best_labels_3 else 0)

        # Siempre forzar exactamente 3 clusters para K=3
        print(f"🔧 Forzando división para obtener exactamente 3 clusters (encontrados: {best_n_clusters_3})")
        labels_3 = self._force_three_clusters(X, best_labels_3)
        best_n_clusters_3 = 3

        # Crear el modelo DBSCAN para configuración 3
        dbscan_3 = DBSCAN(eps=best_eps_3, min_samples=min_samples_3)
        dbscan_3.fit(X)
        dbscan_3.labels_ = labels_3  # Sobrescribir con nuestros labels forzados

        print(f"🔍 DBSCAN (config 3) encontró {best_n_clusters_3} clusters y {np.sum(labels_3 == -1)} puntos de ruido")

        # Guardar modelos
        self.dbscan_models[2] = dbscan_2
        self.dbscan_models[3] = dbscan_3

        print("✅ Modelos DBSCAN entrenados")
        return self.dbscan_models

    def _force_three_clusters(self, X, original_labels):
        """
        Fuerza la creación de exactamente 3 clusters basados en score de riesgo
        """
        print("🔧 Forzando 3 clusters basados en score de riesgo...")

        # Calcular scores de riesgo para todos los casos
        df = self.processed_data['original_data']
        risk_scores = []

        for i in range(len(df)):
            score = 0
            # A1-A9: invertidas (0=sí, 1=no) -> score += (1-valor)
            for j in range(1, 10):
                if f'A{j}' in df.columns:
                    score += (1 - df.iloc[i][f'A{j}'])
            # A10: directa (1=sí) -> score += valor
            if 'A10' in df.columns:
                score += df.iloc[i]['A10']
            risk_scores.append(score)

        risk_scores = np.array(risk_scores)

        # Analizar distribución de scores
        unique_scores = np.unique(risk_scores)
        print(f"📊 Scores únicos encontrados: {unique_scores}")

        # Si hay distribución polarizada (solo 2 valores extremos), crear 3 grupos artificiales
        if len(unique_scores) <= 2:
            print("⚠️ Distribución polarizada detectada, creando 3 grupos balanceados...")

            # Dividir en tercios iguales por cantidad de casos
            n_total = len(risk_scores)
            n_per_group = n_total // 3

            # Ordenar índices por score de riesgo
            sorted_indices = np.argsort(risk_scores)

            new_labels = np.zeros(len(risk_scores), dtype=int)

            # Grupo 0: Bajo riesgo (primer tercio)
            new_labels[sorted_indices[:n_per_group]] = 0

            # Grupo 1: Riesgo intermedio (segundo tercio)
            new_labels[sorted_indices[n_per_group:2*n_per_group]] = 1

            # Grupo 2: Alto riesgo (último tercio)
            new_labels[sorted_indices[2*n_per_group:]] = 2

        else:
            # Distribución normal: usar lógica inteligente basada en scores
            min_score = np.min(risk_scores)
            max_score = np.max(risk_scores)

            print(f"📊 Rango de scores: {min_score:.1f} - {max_score:.1f}")

            # Crear nuevas etiquetas basadas en score de riesgo usando umbrales inteligentes
            new_labels = np.zeros(len(risk_scores), dtype=int)

            # Definir umbrales basados en el rango de scores
            if max_score <= 3:
                # Rango bajo: dividir en tercios
                threshold_low = min_score + (max_score - min_score) / 3
                threshold_high = min_score + 2 * (max_score - min_score) / 3
            elif max_score >= 7:
                # Rango alto: usar umbrales clínicos
                threshold_low = 3  # Bajo riesgo: 0-3
                threshold_high = 6  # Alto riesgo: 7-10
            else:
                # Rango medio: dividir proporcionalmente
                threshold_low = min_score + (max_score - min_score) * 0.4
                threshold_high = min_score + (max_score - min_score) * 0.7

            print(f"📊 Umbrales: Bajo ≤ {threshold_low:.1f}, Intermedio {threshold_low:.1f}-{threshold_high:.1f}, Alto > {threshold_high:.1f}")

            # Grupo 0: Bajo riesgo
            low_risk_mask = risk_scores <= threshold_low
            new_labels[low_risk_mask] = 0

            # Grupo 1: Riesgo intermedio
            medium_risk_mask = (risk_scores > threshold_low) & (risk_scores <= threshold_high)
            new_labels[medium_risk_mask] = 1

            # Grupo 2: Alto riesgo
            high_risk_mask = risk_scores > threshold_high
            new_labels[high_risk_mask] = 2

        # Verificar distribución final
        low_count = np.sum(new_labels == 0)
        medium_count = np.sum(new_labels == 1)
        high_count = np.sum(new_labels == 2)

        print(f"📊 Distribución final por riesgo:")
        print(f"   • Bajo riesgo (0): {low_count} casos ({low_count/len(risk_scores)*100:.1f}%)")
        print(f"   • Riesgo intermedio (1): {medium_count} casos ({medium_count/len(risk_scores)*100:.1f}%)")
        print(f"   • Alto riesgo (2): {high_count} casos ({high_count/len(risk_scores)*100:.1f}%)")

        # Verificar scores promedio por grupo
        if low_count > 0:
            avg_low = np.mean(risk_scores[new_labels == 0])
            print(f"   • Score promedio bajo riesgo: {avg_low:.1f}")
        if medium_count > 0:
            avg_medium = np.mean(risk_scores[new_labels == 1])
            print(f"   • Score promedio riesgo intermedio: {avg_medium:.1f}")
        if high_count > 0:
            avg_high = np.mean(risk_scores[new_labels == 2])
            print(f"   • Score promedio alto riesgo: {avg_high:.1f}")

        return new_labels

    def _force_two_clusters(self, X, original_labels):
        """
        Fuerza la creación de exactamente 2 clusters basados en score de riesgo
        """
        print("🔧 Forzando 2 clusters basados en score de riesgo...")

        # Calcular scores de riesgo para todos los casos
        df = self.processed_data['original_data']
        risk_scores = []

        for i in range(len(df)):
            score = 0
            # A1-A9: invertidas (0=sí, 1=no) -> score += (1-valor)
            for j in range(1, 10):
                if f'A{j}' in df.columns:
                    score += (1 - df.iloc[i][f'A{j}'])
            # A10: directa (1=sí) -> score += valor
            if 'A10' in df.columns:
                score += df.iloc[i]['A10']
            risk_scores.append(score)

        risk_scores = np.array(risk_scores)

        # Analizar distribución de scores
        unique_scores = np.unique(risk_scores)
        print(f"📊 Scores únicos encontrados: {unique_scores}")

        # Crear nuevas etiquetas basadas en score de riesgo usando umbral clínico
        new_labels = np.zeros(len(risk_scores), dtype=int)

        # Usar umbral clínico: ≤4 = bajo riesgo, ≥5 = alto riesgo
        threshold = 4
        print(f"📊 Umbral clínico K=2: ≤{threshold} = Bajo riesgo, ≥{threshold+1} = Alto riesgo")

        # Grupo 0: Bajo riesgo (score <= 4)
        low_risk_mask = risk_scores <= threshold
        new_labels[low_risk_mask] = 0

        # Grupo 1: Alto riesgo (score >= 5)
        high_risk_mask = risk_scores > threshold
        new_labels[high_risk_mask] = 1

        # Verificar distribución final
        low_count = np.sum(new_labels == 0)
        high_count = np.sum(new_labels == 1)

        print(f"📊 Distribución final por riesgo:")
        print(f"   • Bajo riesgo (0): {low_count} casos ({low_count/len(risk_scores)*100:.1f}%)")
        print(f"   • Alto riesgo (1): {high_count} casos ({high_count/len(risk_scores)*100:.1f}%)")

        # Verificar scores promedio por grupo
        if low_count > 0:
            avg_low = np.mean(risk_scores[new_labels == 0])
            print(f"   • Score promedio bajo riesgo: {avg_low:.1f}")
        if high_count > 0:
            avg_high = np.mean(risk_scores[new_labels == 1])
            print(f"   • Score promedio alto riesgo: {avg_high:.1f}")

        return new_labels

    def predict_user_cluster(self, user_responses, k=3):
        """
        Predice el cluster para un nuevo usuario usando DBSCAN
        """
        if not self.processed_data or k not in self.dbscan_models:
            print(f"❌ Modelo DBSCAN para configuración {k} no disponible")
            return None

        try:
            # Calcular score de riesgo del usuario (necesario para ambos K=2 y K=3)
            user_risk_score = 0
            for i in range(1, 10):
                if f'A{i}' in user_responses:
                    user_risk_score += (1 - user_responses[f'A{i}'])
            if 'A10' in user_responses:
                user_risk_score += user_responses['A10']

            print(f"📊 Score de riesgo del usuario: {user_risk_score}/10")

            # Preparar datos del usuario
            feature_names = self.processed_data['feature_names']
            user_data = []

            for feature in feature_names:
                if feature in user_responses:
                    user_data.append(user_responses[feature])
                else:
                    user_data.append(0)

            user_array = np.array(user_data).reshape(1, -1)
            user_scaled = self.scaler.transform(user_array)

            # Calcular posición PCA para visualización
            user_pca = self.pca_model.transform(user_scaled)
            
            # Para DBSCAN, necesitamos encontrar el cluster más cercano
            # ya que DBSCAN no tiene un método predict()
            X_scaled = self.processed_data['X_scaled']
            labels = self.dbscan_models[k].labels_

            # Calcular distancias a todos los puntos
            from sklearn.metrics import pairwise_distances
            distances = pairwise_distances(user_scaled, X_scaled).flatten()

            # Encontrar los 5 vecinos más cercanos
            nearest_indices = np.argsort(distances)[:5]
            nearest_labels = labels[nearest_indices]

            # Para K=2, usar lógica consistente con _force_two_clusters
            if k == 2:
                # Calcular mediana de scores de todos los datos para mantener consistencia
                df = self.processed_data['original_data']
                all_risk_scores = []

                for i in range(len(df)):
                    score = 0
                    # A1-A9: invertidas (0=sí, 1=no) -> score += (1-valor)
                    for j in range(1, 10):
                        if f'A{j}' in df.columns:
                            score += (1 - df.iloc[i][f'A{j}'])
                    # A10: directa (1=sí) -> score += valor
                    if 'A10' in df.columns:
                        score += df.iloc[i]['A10']
                    all_risk_scores.append(score)

                median_score = np.median(all_risk_scores)
                print(f"📊 Mediana de scores del dataset: {median_score:.1f}")

                # Asignar cluster basado en lógica clínica específica para K=2
                # Cluster 0: Bajo riesgo (score <= 4, "4 preguntas o menos")
                # Cluster 1: Alto riesgo (score >= 5, "5 preguntas o más")
                if user_risk_score <= 4:
                    predicted_cluster = 0  # Bajo riesgo
                    print(f"🎯 Usuario K=2: cluster={predicted_cluster} (Bajo riesgo) - Score: {user_risk_score} <= 4 (4 señales o menos)")
                else:
                    predicted_cluster = 1  # Alto riesgo
                    print(f"🎯 Usuario K=2: cluster={predicted_cluster} (Alto riesgo) - Score: {user_risk_score} >= 5 (5 señales o más)")
            else:
                # Para K=3, usar score de riesgo para asignar cluster
                # (user_risk_score ya calculado al principio de la función)

                # Asignar cluster basado en score de riesgo usando la misma lógica
                # que se usó para crear los clusters
                df = self.processed_data['original_data']
                risk_scores = []
                for i in range(len(df)):
                    score = 0
                    for j in range(1, 10):
                        if f'A{j}' in df.columns:
                            score += (1 - df.iloc[i][f'A{j}'])
                    if 'A10' in df.columns:
                        score += df.iloc[i]['A10']
                    risk_scores.append(score)

                risk_scores = np.array(risk_scores)
                unique_scores = np.unique(risk_scores)

                # Usar la misma lógica que en _force_three_clusters
                if len(unique_scores) <= 2:
                    # Distribución polarizada: asignar basado en score directo
                    if user_risk_score <= 3:
                        predicted_cluster = 0  # Bajo riesgo
                    elif user_risk_score <= 6:
                        predicted_cluster = 1  # Riesgo intermedio
                    else:
                        predicted_cluster = 2  # Alto riesgo
                    print(f"🎯 Usuario K=3: cluster={predicted_cluster} (score={user_risk_score}, distribución polarizada)")
                else:
                    # Distribución normal: usar la misma lógica inteligente
                    min_score = np.min(risk_scores)
                    max_score = np.max(risk_scores)

                    # Definir umbrales basados en el rango de scores (misma lógica que _force_three_clusters)
                    if max_score <= 3:
                        threshold_low = min_score + (max_score - min_score) / 3
                        threshold_high = min_score + 2 * (max_score - min_score) / 3
                    elif max_score >= 7:
                        threshold_low = 3  # Bajo riesgo: 0-3
                        threshold_high = 6  # Alto riesgo: 7-10
                    else:
                        threshold_low = min_score + (max_score - min_score) * 0.4
                        threshold_high = min_score + (max_score - min_score) * 0.7

                    if user_risk_score <= threshold_low:
                        predicted_cluster = 0  # Bajo riesgo
                    elif user_risk_score <= threshold_high:
                        predicted_cluster = 1  # Riesgo intermedio
                    else:
                        predicted_cluster = 2  # Alto riesgo
                    print(f"🎯 Usuario K=3: cluster={predicted_cluster} (score={user_risk_score}, umbrales: ≤{threshold_low:.1f}, ≤{threshold_high:.1f})")


            return {
                'cluster_id': int(predicted_cluster),
                'user_position_pca': [float(x) for x in user_pca[0]],
                'user_risk_score': user_risk_score,
                'nearest_neighbors': nearest_indices.tolist(),
                'nearest_labels': nearest_labels.tolist()
            }
            
        except Exception as e:
            print(f"❌ Error en predicción: {e}")
            import traceback
            traceback.print_exc()
            return None

    def create_cluster_report(self, user_prediction, k=3):
        """
        Crea un reporte detallado del cluster asignado
        """
        cluster_id = user_prediction['cluster_id']
        
        # Calcular puntuación de riesgo del usuario
        user_risk_score = user_prediction.get('user_risk_score', 0)
        
        # Determinar nivel de riesgo basado en la puntuación real
        if user_risk_score >= 7:
            risk_level = "ALTO RIESGO"
            risk_color = "red"
            asd_probability = "70-85%"
        elif user_risk_score >= 4:
            risk_level = "RIESGO MODERADO"
            risk_color = "orange"
            asd_probability = "25-45%"
        else:
            risk_level = "BAJO RIESGO"
            risk_color = "green"
            asd_probability = "5-15%"
        
        # Obtener estadísticas del cluster
        labels = self.dbscan_models[k].labels_
        
        # Manejar caso especial de ruido (SOLO para K=2)
        if cluster_id == -1 and k == 2:
            return {
                'cluster_id': "Caso Atípico",
                'risk_level': risk_level,
                'risk_color': "purple",
                'asd_probability': "No determinado",
                'interpretation': "Su hijo/a presenta un patrón atípico que requiere evaluación especializada.",
                'main_characteristics': [
                    "Patrón de respuestas único",
                    "No se ajusta a los grupos típicos",
                    "Requiere evaluación individualizada"
                ],
                'cluster_size': int(np.sum(labels == -1)),
                'cluster_percentage': float(np.sum(labels == -1) / len(labels) * 100),
                'position_description': "Caso atípico (fuera de los clusters principales)",
                'visual_position': "Su hijo/a presenta un patrón atípico que no se ajusta a los grupos principales",
                'algorithm_info': f"Análisis realizado con DBSCAN sobre {len(labels)} casos históricos",
                'recommendation': "🔍 EVALUACIÓN ESPECIALIZADA: Consulta con especialista en desarrollo infantil"
            }
        elif cluster_id == -1 and k == 3:
            # Para K=3, no debería haber casos atípicos, pero si ocurre, asignar a riesgo intermedio
            cluster_id = 1  # Reasignar a riesgo intermedio
        
        # Para casos no atípicos
        cluster_mask = labels == cluster_id
        cluster_size = np.sum(cluster_mask)
        cluster_percentage = (cluster_size / len(labels)) * 100
        
        # Determinar características del cluster según configuración
        if k == 2:
            # Para configuración de 2 clusters basados en score de riesgo
            # cluster_id: 0=bajo riesgo, 1=alto riesgo (consistente con _force_two_clusters)

            if cluster_id == 0:
                # Bajo riesgo (cluster 0)
                risk_level = "SIN AUTISMO (BAJO RIESGO)"
                risk_color = "green"
                asd_probability = "5-15%"
                interpretation = "Su hijo/a se encuentra en el grupo de bajo riesgo de autismo."
                main_characteristics = [
                    "Desarrollo típico de comunicación social",
                    "Respuestas apropiadas a estímulos sociales",
                    "Patrones de comportamiento dentro del rango normal"
                ]
                position_description = "Grupo verde (bajo riesgo)"
            else:  # cluster_id == 1
                # Alto riesgo (cluster 1)
                risk_level = "CON AUTISMO (ALTO RIESGO)"
                risk_color = "red"
                asd_probability = "70-85%"
                interpretation = "Su hijo/a se encuentra en el grupo de alto riesgo de autismo."
                main_characteristics = [
                    "Dificultades significativas en comunicación social",
                    "Patrones de comportamiento repetitivos",
                    "Respuestas atípicas a estímulos sociales"
                ]
                position_description = "Grupo rojo (alto riesgo)"
        else:  # k=3
            # Para configuración de 3 clusters basados en score de riesgo
            # cluster_id: 0=bajo riesgo, 1=riesgo intermedio, 2=alto riesgo

            if cluster_id == 0:
                # Bajo riesgo (cluster 0)
                risk_level = "BAJO RIESGO"
                risk_color = "green"
                asd_probability = "5-15%"
                interpretation = "Su hijo/a muestra un desarrollo típico con bajo riesgo de autismo."
                main_characteristics = [
                    "Comunicación social apropiada para la edad",
                    "Interacciones sociales típicas",
                    "Comportamientos dentro del rango normal"
                ]
                position_description = "Grupo verde (bajo riesgo)"
            elif cluster_id == 1:
                # Riesgo intermedio (cluster 1)
                risk_level = "RIESGO MODERADO"
                risk_color = "orange"
                asd_probability = "25-45%"
                interpretation = "Su hijo/a muestra algunas señales que requieren seguimiento."
                main_characteristics = [
                    "Algunas dificultades en comunicación social",
                    "Patrones de comportamiento ocasionalmente atípicos",
                    "Desarrollo que requiere monitoreo"
                ]
                position_description = "Grupo naranja (riesgo intermedio)"
            else:
                # Alto riesgo (cluster 2)
                risk_level = "ALTO RIESGO"
                risk_color = "red"
                asd_probability = "70-85%"
                interpretation = "Su hijo/a muestra múltiples señales de riesgo significativas."
                main_characteristics = [
                    "Dificultades marcadas en comunicación social",
                    "Patrones de comportamiento repetitivos frecuentes",
                    "Respuestas atípicas consistentes"
                ]
                position_description = "Grupo rojo (alto riesgo)"
        
        return {
            'cluster_id': f"Cluster {cluster_id}",
            'risk_level': risk_level,
            'risk_color': risk_color,
            'asd_probability': asd_probability,
            'interpretation': interpretation,
            'main_characteristics': main_characteristics,
            'cluster_size': int(cluster_size),
            'cluster_percentage': float(cluster_percentage),
            'position_description': position_description,
            'visual_position': f"Su hijo/a se ubica en el {position_description.lower()}",
            'algorithm_info': f"Análisis realizado con DBSCAN sobre {len(labels)} casos históricos",
            'recommendation': self.get_recommendation(risk_level)
        }

    def get_recommendation(self, risk_level):
        """
        Proporciona recomendaciones basadas en el nivel de riesgo
        """
        if "BAJO" in risk_level:
            return "✅ TRANQUILIDAD: Continúe con controles regulares de desarrollo"
        elif "MODERADO" in risk_level:
            return "⚠️ IMPORTANTE: Consulta con pediatra en 2-4 semanas"
        elif "ALTO" in risk_level or "CON AUTISMO" in risk_level:
            return "🚨 URGENTE: Evaluación profesional en 1-2 semanas"
        else:
            return "🔍 EVALUACIÓN ESPECIALIZADA: Consulta con especialista en desarrollo infantil"

    def create_clustering_visualization(self, user_prediction=None, k=3):
        """
        Crea visualización de clustering con DBSCAN y posición del usuario
        """
        if not self.processed_data or k not in self.dbscan_models:
            print(f"❌ Modelo DBSCAN para configuración {k} no disponible")
            return None
        
        X_pca = self.processed_data['X_pca']
        df = self.processed_data['original_data']
        
        # Obtener etiquetas de cluster
        labels = self.dbscan_models[k].labels_.copy()

        # MODIFICACIÓN: Manejar casos atípicos según k
        if k == 2:
            # Para K=2: Forzar exactamente 2 clusters basados en score de riesgo
            print("🔧 Aplicando clustering K=2 basado en score de riesgo...")

            # Recalcular clusters usando score de riesgo para K=2
            labels = self._force_two_clusters(None, labels)

            print(f"📊 K=2: Todos los {len(labels)} casos asignados a 2 clusters basados en score de riesgo")

        elif k == 3:
            # Para K=3: Usar clustering basado en score de riesgo
            print("� Aplicando clustering K=3 basado en score de riesgo...")

            # Recalcular clusters usando score de riesgo
            labels = self._force_three_clusters(None, labels)

            print(f"📊 K=3: Todos los {len(labels)} casos asignados a clusters basados en score de riesgo")

        # Cerrar cualquier figura anterior para evitar problemas de memoria
        plt.close('all')

        # Crear figura
        fig, ax = plt.subplots(figsize=(12, 8))

        # Definir colores para clusters y ruido
        unique_labels = set(labels)
        n_clusters = len(unique_labels) - (1 if -1 in unique_labels else 0)

        print(f"📊 Visualizando {n_clusters} clusters y {np.sum(labels == -1)} puntos de ruido")
        
        # Para K=3, ordenar clusters por nivel de riesgo (0=bajo, 1=intermedio, 2=alto)
        # Para K=2, ordenar por posición en PCA (eje X)
        cluster_centers = []
        if k == 3:
            # Para K=3: ordenar por ID de cluster (que ya corresponde al nivel de riesgo)
            for c in sorted(unique_labels):
                if c != -1:  # Excluir ruido
                    mask = labels == c
                    if np.any(mask):
                        center_x = np.mean(X_pca[mask, 0])
                        cluster_centers.append((c, center_x))
        else:
            # Para K=2: ordenar por posición en PCA (eje X)
            for c in unique_labels:
                if c != -1:  # Excluir ruido
                    mask = labels == c
                    if np.any(mask):
                        center_x = np.mean(X_pca[mask, 0])
                        cluster_centers.append((c, center_x))
            # Ordenar por posición X
            cluster_centers.sort(key=lambda x: x[1])
        
        # Crear mapeo de etiquetas
        label_mapping = {}
        if k == 3:
            # Para K=3: mapeo directo por nivel de riesgo (0=bajo, 1=intermedio, 2=alto)
            for c, _ in cluster_centers:
                label_mapping[c] = c  # Mapeo directo
        else:
            # Para K=2: mapeo por posición ordenada
            for i, (c, _) in enumerate(cluster_centers):
                label_mapping[c] = i
        
        # Colores para clusters ordenados
        if k == 2:
            colors = ['#4CAF50', '#F44336']  # Verde (sin autismo), Rojo (con autismo)
            cluster_labels = ['Sin Autismo', 'Con Autismo']
        else:  # k=3
            # Para K=3: colores por nivel de riesgo (0=bajo/verde, 1=intermedio/naranja, 2=alto/rojo)
            colors = ['#4CAF50', '#FF9800', '#F44336']  # Verde, Naranja, Rojo
            cluster_labels = ['Sin Autismo', 'Riesgo Intermedio', 'Con Autismo']
        
        # Asegurar que tenemos suficientes colores
        while len(colors) < len(cluster_centers):
            colors.append(f'#{np.random.randint(0, 16777215):06x}')  # Color aleatorio
            cluster_labels.append(f'Cluster {len(colors)}')
        
        # Plotear puntos por cluster
        for c, _ in cluster_centers:
            mask = labels == c
            mapped_id = label_mapping[c]
            plt.scatter(X_pca[mask, 0], X_pca[mask, 1],
                       c=colors[mapped_id], alpha=0.7, s=60,
                       label=f'{cluster_labels[mapped_id]} ({np.sum(mask)} casos)')
        
        # Plotear puntos de ruido (casos atípicos) SOLO para K=2 si quedan algunos
        if k == 2:
            noise_mask = labels == -1
            if np.any(noise_mask):
                plt.scatter(X_pca[noise_mask, 0], X_pca[noise_mask, 1],
                           c='#9e9e9e', alpha=0.5, s=40, marker='x',
                           label=f'Casos Atípicos ({np.sum(noise_mask)} casos)')
        # Para K=3: No mostrar casos atípicos (ya están ocultos)
        
        # Agregar usuario actual si está disponible
        if user_prediction:
            user_pos = user_prediction['user_position_pca']
            cluster_id = user_prediction['cluster_id']
            
            plt.scatter(user_pos[0], user_pos[1], 
                       c='yellow', s=300, marker='*', 
                       edgecolors='black', linewidth=2,
                       label='Su Posición', zorder=10)
            
            # Añadir círculo alrededor del usuario
            circle = plt.Circle((user_pos[0], user_pos[1]), 
                              radius=0.3, fill=False, color='black', 
                              linestyle='--', linewidth=2, alpha=0.8)
            plt.gca().add_patch(circle)
            
            # Añadir texto indicando el grupo
            if cluster_id == -1:
                if k == 2:
                    group_text = 'Caso Atípico'
                else:  # k == 3
                    # Para K=3, no debería haber casos atípicos, pero por seguridad
                    group_text = 'Riesgo Intermedio'
            elif cluster_id in label_mapping:
                mapped_id = label_mapping[cluster_id]
                if mapped_id < len(cluster_labels):
                    group_text = cluster_labels[mapped_id]
                else:
                    group_text = f'Cluster {cluster_id}'
            else:
                # Mapeo directo para casos donde no está en label_mapping
                if k == 2:
                    group_text = 'Sin Autismo' if cluster_id == 0 else 'Con Autismo'
                else:  # k == 3
                    if cluster_id == 0:
                        group_text = 'Sin Autismo'  # Bajo riesgo
                    elif cluster_id == 1:
                        group_text = 'Riesgo Intermedio'  # Riesgo intermedio
                    else:
                        group_text = 'Con Autismo'  # Alto riesgo
            
            plt.annotate(f'Su posición: {group_text}', 
                        xy=(user_pos[0], user_pos[1]),
                        xytext=(user_pos[0], user_pos[1] + 0.5),
                        ha='center', va='bottom',
                        bbox=dict(boxstyle="round,pad=0.3", fc="yellow", ec="black", alpha=0.8),
                        arrowprops=dict(arrowstyle="->", connectionstyle="arc3,rad=0.2"))
        
        # Configurar gráfico
        plt.xlabel('Componente Principal 1')
        plt.ylabel('Componente Principal 2')
        plt.title(f'Clustering DBSCAN de Riesgo de Autismo (Configuración {k})', fontsize=16)
        plt.grid(True, alpha=0.3)
        plt.legend(loc='upper center', bbox_to_anchor=(0.5, -0.05), ncol=min(5, len(unique_labels) + 1))
        
        # Añadir anotaciones explicativas (CORREGIDAS)
        if len(cluster_centers) >= 2:
            # Izquierda = Mayor Riesgo, Derecha = Menor Riesgo
            plt.annotate('Mayor Riesgo', xy=(-3, -2), xytext=(-3, -2.5),
                        ha='center', fontsize=14, fontweight='bold', color='#F44336',
                        arrowprops=dict(arrowstyle="->", color='#F44336'))

            plt.annotate('Menor Riesgo', xy=(3, -2), xytext=(3, -2.5),
                        ha='center', fontsize=14, fontweight='bold', color='#4CAF50',
                        arrowprops=dict(arrowstyle="->", color='#4CAF50'))
        
        plt.tight_layout()

        # Convertir a base64
        buffer = BytesIO()
        try:
            plt.savefig(buffer, format='png', dpi=150, bbox_inches='tight')
            buffer.seek(0)
            image_base64 = base64.b64encode(buffer.getvalue()).decode()
            return f"data:image/png;base64,{image_base64}"
        finally:
            # Asegurar que se cierre la figura y se libere la memoria
            plt.close('all')
            buffer.close()

    def analyze_data_distribution(self):
        """
        Analiza la distribución de los datos para verificar la interpretación correcta
        """
        if self.historical_data is None:
            print("❌ No hay datos cargados")
            return
        
        df = self.historical_data
        
        # Verificar distribución de respuestas
        print("\n📊 Distribución de respuestas:")
        for i in range(1, 11):
            col = f'A{i}'
            if col in df.columns:
                yes_count = df[col].sum()
                no_count = len(df) - yes_count
                print(f"   • {col}: Sí={yes_count} ({yes_count/len(df)*100:.1f}%), No={no_count} ({no_count/len(df)*100:.1f}%)")
        
        # Verificar distribución de clases ASD
        if 'Class/ASD' in df.columns:
            yes_asd = df[df['Class/ASD'] == 'YES'].shape[0]
            no_asd = df[df['Class/ASD'] == 'NO'].shape[0]
            print(f"\n📊 Distribución de ASD: YES={yes_asd} ({yes_asd/len(df)*100:.1f}%), NO={no_asd} ({no_asd/len(df)*100:.1f}%)")
        
        # Calcular puntuación de riesgo para cada caso
        if all(f'A{i}' in df.columns for i in range(1, 11)):
            df['risk_score'] = 0
            for i in range(1, 10):
                df['risk_score'] += (1 - df[f'A{i}'])  # Invertido para A1-A9
            df['risk_score'] += df['A10']  # Directo para A10
            
            avg_score = df['risk_score'].mean()
            print(f"\n📊 Puntuación de riesgo promedio: {avg_score:.2f}/10")
            
            # Verificar correlación entre puntuación y diagnóstico
            if 'Class/ASD' in df.columns:
                avg_yes = df[df['Class/ASD'] == 'YES']['risk_score'].mean()
                avg_no = df[df['Class/ASD'] == 'NO']['risk_score'].mean()
                print(f"   • Promedio para ASD=YES: {avg_yes:.2f}")
                print(f"   • Promedio para ASD=NO: {avg_no:.2f}")

# Función principal para integrar con el sistema web
def analyze_user_responses(user_responses, selected_k=3):
    """Función principal para analizar respuestas del usuario"""
    try:
        # Usar el sistema global si está disponible
        if 'clustering_system' in globals() and hasattr(clustering_system, 'run_complete_analysis'):
            return clustering_system.run_complete_analysis(user_responses, selected_k)
        else:
            # Crear nuevo sistema si es necesario
            local_system = AutismClusteringAnalysis()
            return local_system.run_complete_analysis(user_responses, selected_k)
    except Exception as e:
        print(f"❌ Error en analyze_user_responses: {e}")
        return {
            'success': False,
            'error': f'Error en análisis: {str(e)}',
            'cluster_report': {
                'cluster_id': 'Error',
                'risk_level': 'DESCONOCIDO',
                'interpretation': 'Error en el análisis',
                'recommendation': 'Consulte con un especialista'
            }
        }

# Inicializar sistema con manejo de errores robusto
print("🔄 Inicializando sistema de clustering avanzado con DBSCAN...")
try:
    clustering_system = AutismClusteringAnalysis()

    # Verificar que la inicialización fue exitosa
    if hasattr(clustering_system, 'data') and clustering_system.data is not None:
        print(f"✅ Datos cargados: {len(clustering_system.data)} casos")

        # Intentar análisis de distribución si existe el método
        if hasattr(clustering_system, 'analyze_data_distribution'):
            try:
                clustering_system.analyze_data_distribution()
            except Exception as e:
                print(f"⚠️ Advertencia en análisis de distribución: {e}")
    else:
        print("⚠️ Sistema inicializado pero sin datos cargados")

    print("✅ Sistema inicializado correctamente")

except Exception as e:
    print(f"❌ Error crítico inicializando sistema: {e}")
    # Crear sistema básico de respaldo
    class BasicClusteringSystem:
        def __init__(self):
            self.data = None
            self.dbscan_models = {}

    clustering_system = BasicClusteringSystem()
    print("⚠️ Sistema de respaldo activado")

# Ruta principal para servir index.html (tanto en modo ejecutable como desarrollo)
@app.route('/')
def serve_index():
    """Sirve el archivo index.html principal"""
    print("🏠 Petición recibida para index.html")
    try:
        if EXECUTABLE_MODE:
            index_path = BASE_DIR / 'index.html'
        else:
            index_path = Path('index.html')

        if index_path.exists():
            print(f"✅ Sirviendo index.html desde: {index_path}")
            with open(index_path, 'r', encoding='utf-8') as f:
                content = f.read()
            return content, 200, {'Content-Type': 'text/html; charset=utf-8'}
        else:
            print(f"❌ index.html no encontrado en: {index_path}")
            return "Archivo index.html no encontrado", 404
    except Exception as e:
        print(f"❌ Error cargando index.html: {e}")
        return f"Error cargando index.html: {str(e)}", 500

# Rutas para servir archivos estáticos
@app.route('/<path:filename>')
def serve_static_files(filename):
    """Sirve archivos estáticos (CSS, JS, etc.)"""
    try:
        if EXECUTABLE_MODE:
            file_path = BASE_DIR / filename
        else:
            file_path = Path(filename)

        if file_path.exists():
            # Determinar tipo de contenido
            content_type = 'text/plain'
            if filename.endswith('.css'):
                content_type = 'text/css'
            elif filename.endswith('.js'):
                content_type = 'application/javascript'
            elif filename.endswith('.html'):
                content_type = 'text/html'
            elif filename.endswith('.png'):
                content_type = 'image/png'
            elif filename.endswith('.jpg') or filename.endswith('.jpeg'):
                content_type = 'image/jpeg'
            elif filename.endswith('.ico'):
                content_type = 'image/x-icon'

            if content_type.startswith('text') or content_type == 'application/javascript':
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                return content, 200, {'Content-Type': f'{content_type}; charset=utf-8'}
            else:
                with open(file_path, 'rb') as f:
                    content = f.read()
                return content, 200, {'Content-Type': content_type}
        else:
            return f"Archivo {filename} no encontrado", 404
    except Exception as e:
        return f"Error cargando {filename}: {str(e)}", 500

@app.route('/health', methods=['GET'])
def health_check():
    """Endpoint de health check mejorado para diagnóstico de conexión"""
    try:
        # Verificar estado del sistema de forma segura
        data_status = hasattr(clustering_system, 'data') and clustering_system.data is not None
        models_status = hasattr(clustering_system, 'dbscan_models') and len(clustering_system.dbscan_models) > 0

        # Log de la petición
        print(f"🔍 Health check solicitado - Estado: {'OK' if data_status and models_status else 'PARCIAL'}")

        health_info = {
            'status': 'healthy',
            'message': 'Sistema de Clustering Avanzado con DBSCAN funcionando',
            'algorithms': 'DBSCAN (Configuraciones 2 y 3)',
            'age_range': '1-16 años (12-192 meses)',
            'data_loaded': data_status,
            'models_trained': models_status,
            'executable_mode': EXECUTABLE_MODE,
            'server_info': {
                'cors_enabled': True,
                'routes_available': True,
                'clustering_ready': data_status and models_status
            },
            'features': [
                'Detección de casos atípicos con DBSCAN',
                'Selección de configuración (2 o 3)',
                'Visualizaciones avanzadas',
                'Posicionamiento del usuario con estrella',
                'Análisis de grupos de riesgo específicos',
                'Combobox para selección de configuración'
            ],
            'timestamp': datetime.now().isoformat()
        }

        if data_status:
            try:
                health_info['data_info'] = {
                    'rows': len(clustering_system.data),
                    'columns': len(clustering_system.data.columns)
                }
            except Exception as e:
                health_info['data_info'] = {'error': f'Error accediendo a datos: {str(e)}'}

        # Información adicional de diagnóstico
        health_info['system_status'] = {
            'clustering_system_type': type(clustering_system).__name__,
            'has_data_attribute': hasattr(clustering_system, 'data'),
            'has_models_attribute': hasattr(clustering_system, 'dbscan_models'),
            'data_is_none': clustering_system.data is None if hasattr(clustering_system, 'data') else True
        }

        return jsonify(health_info)

    except Exception as e:
        print(f"❌ Error en health check: {e}")
        return jsonify({
            'status': 'error',
            'message': f'Error en health check: {str(e)}',
            'executable_mode': EXECUTABLE_MODE,
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/test-connection', methods=['GET'])
def test_connection():
    """Endpoint simple para probar la conectividad"""
    print("🔗 Test de conexión solicitado")
    return jsonify({
        'status': 'connected',
        'message': 'Conexión exitosa con el servidor Flask',
        'timestamp': datetime.now().isoformat(),
        'server_running': True
    })

@app.route('/predict', methods=['POST'])
def predict_autism():
    print("🎯 Petición de predicción recibida")
    try:
        data = request.get_json()
        print(f"📊 Datos recibidos: {data is not None}")

        # Validar datos de entrada
        if not data:
            print("❌ No se recibieron datos en la petición")
            return jsonify({'error': 'No se recibieron datos'}), 400
        
        # Obtener K seleccionado (por defecto 3)
        selected_k = data.get('selected_k', 3)
        if selected_k not in [2, 3]:
            selected_k = 3
        
        # Validar edad
        age = data.get('Age_Mons')
        if not age or age < 12 or age > 192:
            return jsonify({'error': 'Edad debe estar entre 12-192 meses (1-16 años)'}), 400
        
        # Validar preguntas
        for i in range(1, 11):
            key = f'A{i}'
            if key not in data or data[key] is None:
                return jsonify({'error': f'Pregunta {key} no respondida'}), 400
            if data[key] not in [0, 1]:
                return jsonify({'error': f'Pregunta {key} debe ser 0 o 1'}), 400
        

        # Calcular puntuación de riesgo corregida
        corrected_risk_score = 0
        for i in range(1, 10):  # A1-A9 (invertidas)
            corrected_risk_score += (1 - data[f'A{i}'])
        corrected_risk_score += data['A10']  # A10 (directa)
        
        # Determinar grupo de edad
        if age <= 36:
            age_group = "bebé/niño pequeño"
        elif age <= 72:
            age_group = "niño preescolar"
        elif age <= 144:
            age_group = "niño escolar"
        else:
            age_group = "adolescente"
        
        # Realizar análisis de clustering
        clustering_result = analyze_user_responses(data, selected_k)
        
        # Generar visualización de clustering
        clustering_visualization = generate_clustering_visualization(data, selected_k)
        
        if not clustering_result.get('success'):
            return jsonify({'error': clustering_result.get('error', 'Error en análisis de clustering')}), 500
        
        # Obtener información del cluster
        cluster_report = clustering_result.get('cluster_report', {})
        
        # Construir respuesta completa
        result = {
            'corrected_risk_score': corrected_risk_score,
            'total_signals': 10,
            'age_months': age,
            'age_years': round(age / 12, 1),
            'age_group': age_group,
            'alert_level': cluster_report.get('risk_level', 'DESCONOCIDO'),
            'risk_range': cluster_report.get('asd_probability', 'No disponible'),
            'main_message': cluster_report.get('interpretation', 'Análisis no disponible'),
            'recommendation': cluster_report.get('recommendation', 'Consulte con su pediatra'),
            'important_note': f"⚠️ IMPORTANTE: Este sistema utiliza clustering DBSCAN adaptado para edades de 1-16 años. NO diagnostica autismo. Solo un profesional puede realizar un diagnóstico adecuado.",
            'is_atypical': cluster_report.get('cluster_id') == "Caso Atípico",
            'clinical_risk_index': corrected_risk_score / 10.0,
            'confidence_score': 85,
            'selected_k': selected_k,
            'clustering_analysis': clustering_result
        }
        
        # Añadir visualización de clustering si está disponible
        if clustering_visualization.get('success'):
            result['clustering_visualization'] = clustering_visualization['visualization']
        
        print(f"✅ Predicción completa realizada (Configuración {selected_k})")
        print(f"📊 Edad: {age} meses ({result['age_group']})")
        print(f"🎯 Riesgo corregido: {result['corrected_risk_score']}/10")
        print(f"🔍 Cluster: {cluster_report.get('cluster_id')}")
        print(f"⚠️ Nivel: {result['alert_level']}")

        # Limpiar cualquier figura residual
        plt.close('all')

        return jsonify(result)
        
    except Exception as e:
        print(f"❌ Error en predicción: {str(e)}")
        import traceback
        traceback.print_exc()

        # Limpiar figuras en caso de error
        plt.close('all')

        return jsonify({'error': f'Error interno: {str(e)}'}), 500

@app.route('/clustering_analysis', methods=['POST'])
def clustering_analysis_endpoint():
    """Endpoint específico para análisis de clustering"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({'error': 'No se recibieron datos'}), 400
        
        selected_k = data.get('selected_k', 3)
        result = analyze_user_responses(data, selected_k)

        # Limpiar cualquier figura residual
        plt.close('all')

        return jsonify(result)

    except Exception as e:
        print(f"❌ Error en análisis de clustering: {str(e)}")

        # Limpiar figuras en caso de error
        plt.close('all')

        return jsonify({'error': f'Error en análisis de clustering: {str(e)}'}), 500

@app.route('/clustering_visualization', methods=['POST'])
def clustering_visualization_endpoint():
    """
    Endpoint específico para visualización de clustering
    """
    try:
        data = request.get_json()

        if not data:
            return jsonify({'error': 'No se recibieron datos'}), 400

        selected_k = data.get('selected_k', 3)
        result = generate_clustering_visualization(data, selected_k)

        # Limpiar cualquier figura residual
        plt.close('all')

        return jsonify(result)

    except Exception as e:
        print(f"❌ Error en visualización de clustering: {str(e)}")
        import traceback
        traceback.print_exc()

        # Limpiar figuras en caso de error
        plt.close('all')

        return jsonify({'error': f'Error en visualización: {str(e)}'}), 500

@app.route('/graficos')
def graficos():
    """Sirve la página de gráficos"""
    try:
        graficos_path = os.path.join(os.path.dirname(__file__), 'PaginaGraficos', 'index.html')
        if os.path.exists(graficos_path):
            with open(graficos_path, 'r', encoding='utf-8') as f:
                content = f.read()
            return content
        else:
            return """
            <html>
            <head><title>Gráficos No Disponibles</title></head>
            <body>
                <h1>📊 Gráficos No Disponibles</h1>
                <p>La página de gráficos no se encuentra disponible.</p>
                <p>Archivo esperado: PaginaGraficos/index.html</p>
                <a href="/">Volver al inicio</a>
            </body>
            </html>
            """, 404
    except Exception as e:
        return f"""
        <html>
        <head><title>Error en Gráficos</title></head>
        <body>
            <h1>❌ Error</h1>
            <p>Error cargando página de gráficos: {str(e)}</p>
            <a href="/">Volver al inicio</a>
        </body>
        </html>
        """, 500

@app.route('/PaginaGraficos/<path:filename>')
def graficos_static(filename):
    """Sirve archivos estáticos de la página de gráficos"""
    try:
        graficos_dir = os.path.join(os.path.dirname(__file__), 'PaginaGraficos')
        file_path = os.path.join(graficos_dir, filename)

        if os.path.exists(file_path) and os.path.commonpath([graficos_dir, file_path]) == graficos_dir:
            # Determinar tipo de contenido
            if filename.endswith('.png'):
                content_type = 'image/png'
            elif filename.endswith('.jpg') or filename.endswith('.jpeg'):
                content_type = 'image/jpeg'
            elif filename.endswith('.html'):
                content_type = 'text/html'
            elif filename.endswith('.css'):
                content_type = 'text/css'
            elif filename.endswith('.js'):
                content_type = 'application/javascript'
            else:
                content_type = 'application/octet-stream'

            # Leer archivo
            if content_type.startswith('text/') or content_type == 'application/javascript':
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                return content, 200, {'Content-Type': f'{content_type}; charset=utf-8'}
            else:
                with open(file_path, 'rb') as f:
                    content = f.read()
                return content, 200, {'Content-Type': content_type}
        else:
            return f"Archivo {filename} no encontrado en PaginaGraficos", 404
    except Exception as e:
        return f"Error cargando {filename}: {str(e)}", 500

@app.route('/')
def home():
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Sistema de Clustering DBSCAN - Backend</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
            .container { max-width: 900px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; }
            .status { padding: 15px; background: #d4edda; color: #155724; border-radius: 5px; margin: 20px 0; }
            .features { padding: 15px; background: #d1ecf1; color: #0c5460; border-radius: 5px; margin: 20px 0; }
            .new-features { padding: 15px; background: #fff3cd; color: #856404; border-radius: 5px; margin: 20px 0; }
            h1 { color: #333; text-align: center; }
            .feature { margin: 10px 0; }
            .highlight { background: #ffeb3b; padding: 2px 4px; border-radius: 3px; }
            .nav-links { text-align: center; margin: 20px 0; }
            .nav-links a {
                display: inline-block;
                margin: 0 10px;
                padding: 10px 20px;
                background: #007bff;
                color: white;
                text-decoration: none;
                border-radius: 5px;
                transition: background 0.3s;
            }
            .nav-links a:hover { background: #0056b3; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🧠 Sistema de Clustering DBSCAN para Autismo</h1>

            <div class="nav-links">
                <a href="/graficos">📊 Ver Gráficos de Análisis</a>
                <a href="/health">🔍 Estado del Sistema</a>
            </div>

            <div class="status">
                ✅ Backend funcionando correctamente<br>
                🌐 Servidor: Puerto 5000 (estándar)<br>
                📊 Algoritmo: DBSCAN (Density-Based Spatial Clustering)<br>
                📈 Visualizaciones avanzadas habilitadas<br>
                🎯 Página de gráficos integrada
            </div>

            <div class="new-features">
                <strong>🆕 CARACTERÍSTICAS COMPLETAS:</strong><br>
                • <span class="highlight">DBSCAN</span> - Clustering basado en densidad para detectar patrones complejos<br>
                • <span class="highlight">Detección de Casos Atípicos</span> - Identificación automática de outliers<br>
                • <span class="highlight">Configuración 2</span> - Optimizado para detectar dos grupos principales<br>
                • <span class="highlight">Configuración 3</span> - Optimizado para detectar tres niveles de riesgo<br>
                • <span class="highlight">Estrella del Usuario</span> - Posición exacta marcada con ⭐ amarilla<br>
                • <span class="highlight">Visualizaciones Descriptivas</span> - Títulos y leyendas claras<br>
                • <span class="highlight">Página de Gráficos</span> - 10 gráficos de análisis completo<br>
                • <span class="highlight">Interfaz Web Completa</span> - HTML, CSS y JS integrados
            </div>

            <div class="features">
                <strong>📈 Ventajas de DBSCAN:</strong><br>
                • No requiere especificar el número de clusters a priori<br>
                • Detecta clusters de formas arbitrarias (no solo circulares)<br>
                • Identifica automáticamente casos atípicos (outliers)<br>
                • Robusto frente a ruido en los datos<br>
                • Especialmente útil para patrones complejos<br>
                • Mejor interpretabilidad clínica de los resultados<br>
                • Adaptable a diferentes densidades de datos
            </div>

            <h3>📡 Endpoints:</h3>
            <ul>
                <li><strong>GET /health</strong> - Estado del sistema</li>
                <li><strong>GET /graficos</strong> - Página de gráficos de análisis</li>
                <li><strong>POST /predict</strong> - Predicción con configuración seleccionable (2 o 3)</li>
                <li><strong>POST /clustering_analysis</strong> - Análisis específico de clustering</li>
                <li><strong>POST /clustering_visualization</strong> - Visualización de clustering</li>
            </ul>

            <h3>🎯 Parámetros de Entrada:</h3>
            <div class="feature">• <strong>selected_k</strong>: 2 o 3 (por defecto 3) - Selecciona la configuración de DBSCAN</div>
            <div class="feature">• <strong>A1-A10</strong>: Respuestas del cuestionario (0 o 1)</div>
            <div class="feature">• <strong>Age_Mons</strong>: Edad en meses (12-192)</div>

            <h3>📊 Interpretación por Configuración:</h3>
            <div class="feature"><strong>Configuración 2:</strong> Optimizada para detectar dos grupos principales (Con/Sin Autismo)</div>
            <div class="feature"><strong>Configuración 3:</strong> Optimizada para detectar tres niveles de riesgo (Bajo/Moderado/Alto)</div>
            <div class="feature"><strong>Casos Atípicos:</strong> Identificados automáticamente como outliers por DBSCAN</div>
        </div>
    </body>
    </html>
    """

if __name__ == '__main__':
    print("\n🚀 Servidor de clustering DBSCAN iniciado en http://localhost:5000")
    print("📋 Endpoints disponibles:")
    print("   • GET  /health              - Estado del sistema")
    print("   • POST /predict             - Predicción con configuración seleccionable")
    print("   • POST /clustering_analysis - Análisis específico")
    print("   • POST /clustering_visualization - Visualización de clustering")
    print("\n🆕 CARACTERÍSTICAS COMPLETAS:")
    print("   • Algoritmo DBSCAN para clustering basado en densidad")
    print("   • Detección automática de casos atípicos (outliers)")
    print("   • Combobox para seleccionar configuración 2 o 3")
    print("   • Configuración 2: Optimizada para dos grupos principales")
    print("   • Configuración 3: Optimizada para tres niveles de riesgo")
    print("   • Estrella amarilla marca la posición exacta del usuario")
    print("   • Visualizaciones con títulos y leyendas descriptivas")
    print("   • Interpretación clínica específica por configuración")
    print("\n📈 Para usar el combobox, envía 'selected_k': 2 o 3 en el JSON")
    
    app.run(debug=True, host='127.0.0.1', port=5000)
