#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Diagnóstico específico para problemas de construcción del ejecutable
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def verificar_pyinstaller():
    """Verifica PyInstaller y sus dependencias"""
    print("🔍 VERIFICANDO PYINSTALLER")
    print("=" * 40)
    
    try:
        import PyInstaller
        print(f"✅ PyInstaller instalado: {PyInstaller.__version__}")
        
        # Verificar que pyinstaller esté en PATH
        result = subprocess.run(['pyinstaller', '--version'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ PyInstaller ejecutable: {result.stdout.strip()}")
        else:
            print("❌ PyInstaller no ejecutable desde línea de comandos")
            return False
            
        return True
        
    except ImportError:
        print("❌ PyInstaller no instalado")
        print("🔧 Instalando PyInstaller...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
            print("✅ PyInstaller instalado")
            return True
        except:
            print("❌ Error instalando PyInstaller")
            return False

def verificar_archivos_necesarios():
    """Verifica archivos necesarios para el build"""
    print("\n🔍 VERIFICANDO ARCHIVOS NECESARIOS")
    print("=" * 40)
    
    archivos_criticos = [
        'main.py',
        'app.py',
        'clustering_visualizer.py',
        'datos.csv',
        'index.html',
        'styles.css',
        'script.js'
    ]
    
    archivos_opcionales = [
        'clustering_ui.js',
        'brain_icon.ico'
    ]
    
    todos_criticos = True
    
    print("📋 Archivos críticos:")
    for archivo in archivos_criticos:
        if os.path.exists(archivo):
            size = os.path.getsize(archivo)
            print(f"   ✅ {archivo} ({size} bytes)")
        else:
            print(f"   ❌ {archivo} - FALTANTE")
            todos_criticos = False
    
    print("\n📋 Archivos opcionales:")
    for archivo in archivos_opcionales:
        if os.path.exists(archivo):
            size = os.path.getsize(archivo)
            print(f"   ✅ {archivo} ({size} bytes)")
        else:
            print(f"   ⚠️ {archivo} - No encontrado (opcional)")
    
    return todos_criticos

def verificar_dependencias():
    """Verifica dependencias de Python"""
    print("\n🔍 VERIFICANDO DEPENDENCIAS")
    print("=" * 40)
    
    dependencias = [
        'flask',
        'pandas', 
        'numpy',
        'scikit-learn',
        'matplotlib',
        'seaborn'
    ]
    
    todas_ok = True
    
    for dep in dependencias:
        try:
            __import__(dep)
            print(f"   ✅ {dep}")
        except ImportError:
            print(f"   ❌ {dep} - NO INSTALADO")
            todas_ok = False
    
    return todas_ok

def test_build_simple():
    """Prueba un build simple para identificar errores"""
    print("\n🔍 PROBANDO BUILD SIMPLE")
    print("=" * 40)
    
    try:
        # Limpiar builds anteriores
        for dir_name in ['build', 'dist']:
            if os.path.exists(dir_name):
                shutil.rmtree(dir_name)
                print(f"🧹 Limpiado directorio {dir_name}")
        
        # Comando básico de PyInstaller
        cmd = [
            'pyinstaller',
            '--onefile',
            '--name=TestBuild',
            '--console',
            '--noconfirm',
            'main.py'
        ]
        
        print("🚀 Ejecutando build básico...")
        print(f"Comando: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("✅ Build básico exitoso")
            
            # Verificar que se creó el ejecutable
            exe_path = Path('dist/TestBuild.exe')
            if exe_path.exists():
                size_mb = exe_path.stat().st_size / (1024 * 1024)
                print(f"✅ Ejecutable creado: {size_mb:.1f} MB")
                return True
            else:
                print("❌ Ejecutable no encontrado después del build")
                return False
        else:
            print("❌ Error en build básico:")
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Timeout en build (5 minutos)")
        return False
    except Exception as e:
        print(f"❌ Error ejecutando build: {e}")
        return False

def analizar_errores_comunes():
    """Analiza errores comunes de PyInstaller"""
    print("\n🔍 ANALIZANDO ERRORES COMUNES")
    print("=" * 40)
    
    problemas_encontrados = []
    
    # 1. Verificar espacio en disco
    try:
        stat = shutil.disk_usage('.')
        free_gb = stat.free / (1024**3)
        if free_gb < 2:
            problemas_encontrados.append(f"Poco espacio libre: {free_gb:.1f} GB")
        else:
            print(f"✅ Espacio libre: {free_gb:.1f} GB")
    except:
        problemas_encontrados.append("No se pudo verificar espacio en disco")
    
    # 2. Verificar permisos de escritura
    try:
        test_file = 'test_permisos.tmp'
        with open(test_file, 'w') as f:
            f.write('test')
        os.remove(test_file)
        print("✅ Permisos de escritura OK")
    except:
        problemas_encontrados.append("Sin permisos de escritura")
    
    # 3. Verificar PATH
    python_path = sys.executable
    if 'python' not in python_path.lower():
        problemas_encontrados.append("Python no en PATH estándar")
    else:
        print(f"✅ Python en PATH: {python_path}")
    
    # 4. Verificar antivirus (común en Windows)
    if os.name == 'nt':  # Windows
        print("⚠️ En Windows: Verifique que el antivirus no bloquee PyInstaller")
    
    if problemas_encontrados:
        print("\n❌ PROBLEMAS ENCONTRADOS:")
        for problema in problemas_encontrados:
            print(f"   • {problema}")
    else:
        print("\n✅ No se encontraron problemas obvios")
    
    return len(problemas_encontrados) == 0

def main():
    """Función principal de diagnóstico"""
    print("🔧 DIAGNÓSTICO DE PROBLEMAS DE BUILD")
    print("🧠 Sistema de Evaluación de Autismo")
    print("=" * 50)
    
    tests = [
        ("PyInstaller", verificar_pyinstaller),
        ("Archivos Necesarios", verificar_archivos_necesarios),
        ("Dependencias Python", verificar_dependencias),
        ("Errores Comunes", analizar_errores_comunes),
        ("Build Simple", test_build_simple),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*15} {test_name} {'='*15}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: PASÓ")
            else:
                print(f"❌ {test_name}: FALLÓ")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
    
    print("\n" + "=" * 50)
    print("📊 RESUMEN DE DIAGNÓSTICO")
    print("=" * 50)
    print(f"✅ Pruebas pasadas: {passed}/{total}")
    
    if passed == total:
        print("🎉 TODO ESTÁ BIEN - El build debería funcionar")
        print("🚀 Puede intentar ejecutar build_exe.py o rebuild_exe_corregido.py")
    elif passed >= 3:
        print("⚠️ PROBLEMAS MENORES - Puede intentar el build")
        print("🔧 Revise los errores mostrados arriba")
    else:
        print("❌ PROBLEMAS GRAVES - Debe corregir antes del build")
        print("🔧 Corrija los problemas críticos mostrados")
    
    print("\n🔧 RECOMENDACIONES:")
    if passed < 3:
        print("1. Instale/actualice PyInstaller: pip install --upgrade pyinstaller")
        print("2. Verifique que todos los archivos estén presentes")
        print("3. Instale dependencias faltantes: pip install -r requirements.txt")
        print("4. Ejecute como administrador si hay problemas de permisos")
    else:
        print("1. Los problemas parecen menores")
        print("2. Intente ejecutar el build nuevamente")
        print("3. Si persiste, revise logs detallados de PyInstaller")
    
    print("=" * 50)
    input("Presione Enter para salir...")

if __name__ == "__main__":
    main()
