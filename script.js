// Variables globales
let currentStep = 0
let formData = {
  A1: null,
  A2: null,
  A3: null,
  A4: null,
  A5: null,
  A6: null,
  A7: null,
  A8: null,
  A9: null,
  A10: null,
  Age_Mons: null,
}
let analysisResult = null
let clusteringAnalysis = null
let API_BASE_URL = "http://127.0.0.1:5000" // Puerto estándar 5000, se detectará automáticamente si está ocupado

// Preguntas del cuestionario
const questions = [
  {
    id: "A1",
    text: "¿Su hijo/a hace contacto visual cuando lo llama por su nombre?",
    description: "Observe si su hijo mira hacia usted cuando dice su nombre",
    category: "Comunicación Social",
    critical: false,
  },
  {
    id: "A2",
    text: "¿Es fácil para usted hacer contacto visual con su hijo/a?",
    description: "Evalúe la facilidad para establecer contacto visual directo",
    category: "Comunicación Social",
    critical: false,
  },
  {
    id: "A3",
    text: "¿Su hijo/a señala para indicar que quiere algo?",
    description: "Observe si usa el dedo índice para señalar objetos deseados",
    category: "Comunicación",
    critical: false,
  },
  {
    id: "A4",
    text: "¿Su hijo/a señala para compartir interés con usted?",
    description: "🔴 CRÍTICO: Señalar para mostrar algo interesante (no solo pedir)",
    category: "Atención Conjunta",
    critical: true,
  },
  {
    id: "A5",
    text: "¿Su hijo/a pretende jugar (ej: cuidar muñecas, hablar por teléfono)?",
    description: "Observe juegos de imitación y simulación",
    category: "Juego Simbólico",
    critical: false,
  },
  {
    id: "A6",
    text: "¿Su hijo/a sigue su mirada cuando usted mira algo?",
    description: "Evalúe si sigue la dirección de su mirada hacia objetos",
    category: "Atención Conjunta",
    critical: false,
  },
  {
    id: "A7",
    text: "¿Su hijo/a trata de consolarlo/a si está triste o lastimado/a?",
    description: "Observe respuestas empáticas hacia el malestar de otros",
    category: "Empatía",
    critical: false,
  },
  {
    id: "A8",
    text: "¿Describiría las primeras palabras de su hijo/a como típicas?",
    description: "Evalúe el desarrollo del lenguaje temprano",
    category: "Lenguaje",
    critical: false,
  },
  {
    id: "A9",
    text: "¿Su hijo/a usa gestos simples (ej: saludar con la mano)?",
    description: "🔴 CRÍTICO: Uso de gestos comunicativos básicos",
    category: "Comunicación No Verbal",
    critical: true,
  },
  {
    id: "A10",
    text: "¿Su hijo/a lo/la mira fijamente sin razón aparente?",
    description: "Observe patrones de mirada inusual o prolongada",
    category: "Comportamiento",
    critical: false,
  },
]

// Inicialización con debugging completo
document.addEventListener("DOMContentLoaded", () => {
  console.log("🧠 Sistema Completo de Evaluación de Autismo con Clustering iniciado")
  console.log("🔧 Verificando elementos del DOM...")

  // Verificar elementos críticos
  const stepContent = document.getElementById("step-content")
  const progressFill = document.getElementById("progress-fill")
  const progressText = document.getElementById("progress-text")
  const prevBtn = document.getElementById("prev-btn")
  const nextBtn = document.getElementById("next-btn")

  console.log("📋 Elementos encontrados:")
  console.log("   • step-content:", stepContent ? "✅" : "❌")
  console.log("   • progress-fill:", progressFill ? "✅" : "❌")
  console.log("   • progress-text:", progressText ? "✅" : "❌")
  console.log("   • prev-btn:", prevBtn ? "✅" : "❌")
  console.log("   • next-btn:", nextBtn ? "✅" : "❌")

  checkBackendConnection()
  showView("welcome")
  setupEventListeners()
})

// Detectar puerto del servidor automáticamente
async function detectServerPort() {
  console.log("🔍 Detectando puerto del servidor...")

  // Puertos a probar en orden de prioridad (5000 primero)
  const commonPorts = [5000, 5001, 5002, 5003, 5004, 5005]

  for (const port of commonPorts) {
    try {
      const testUrl = `http://127.0.0.1:${port}/health`
      console.log(`🔍 Probando puerto ${port}...`)

      const response = await fetch(testUrl, {
        method: 'GET',
        timeout: 2000,
        signal: AbortSignal.timeout(2000)
      })

      if (response.ok) {
        const data = await response.json()
        if (data.status === "healthy") {
          console.log(`✅ Servidor encontrado en puerto ${port}`)
          API_BASE_URL = `http://127.0.0.1:${port}`
          return port
        }
      }
    } catch (error) {
      // Continuar con el siguiente puerto
      continue
    }
  }

  console.log("❌ No se encontró servidor activo en puertos comunes")
  return null
}

// Verificar conexión con backend mejorada
async function checkBackendConnection() {
  const statusElement = document.getElementById("connection-status")
  const textElement = document.getElementById("connection-text")

  try {
    statusElement.className = "connection-status checking"
    textElement.textContent = "Detectando servidor..."

    // Primero intentar detectar el puerto correcto
    const detectedPort = await detectServerPort()

    if (!detectedPort) {
      throw new Error("No se encontró servidor activo")
    }

    // Ahora verificar la conexión con el puerto detectado
    statusElement.textContent = "Verificando conexión..."
    const response = await fetch(`${API_BASE_URL}/health`)
    const data = await response.json()

    if (response.ok && data.status === "healthy") {
      statusElement.className = "connection-status connected"
      textElement.textContent = `Conectado - Puerto ${detectedPort} - Clustering K=2/K=3 Disponible`
      console.log("✅ Conexión establecida con backend Python completo")
      console.log(`🌐 URL del servidor: ${API_BASE_URL}`)
      console.log("📊 Algoritmos disponibles:", data.algorithms)
      console.log("📈 Características:", data.features)
      console.log("🎯 Rango de edad:", data.age_range)

      // Verificar también el endpoint de test-connection
      try {
        const testResponse = await fetch(`${API_BASE_URL}/test-connection`)
        if (testResponse.ok) {
          console.log("✅ Test de conexión exitoso")
        }
      } catch (e) {
        console.log("⚠️ Test de conexión falló, pero health check OK")
      }

    } else {
      throw new Error("Backend no saludable")
    }
  } catch (error) {
    statusElement.className = "connection-status disconnected"
    textElement.textContent = "Sin conexión - Modo local"
    console.warn("⚠️ Backend no disponible, usando modo local")
    console.warn("Error:", error.message)
  }
}

// Configurar event listeners
function setupEventListeners() {
  console.log("🔧 Configurando event listeners...")

  // Event listener para el botón de inicio
  const startBtn = document.getElementById("start-assessment-btn")
  if (startBtn) {
    startBtn.addEventListener("click", (e) => {
      e.preventDefault()
      e.stopPropagation()
      console.log("🚀 Botón iniciar evaluación clickeado")
      startAssessment()
    })
  }

  // Event listeners para navegación principal
  const navHome = document.getElementById("nav-home")
  const navAutism = document.getElementById("nav-autism")
  const navInfo = document.getElementById("nav-info")
  const navToggle = document.getElementById("nav-toggle")

  if (navHome) {
    navHome.addEventListener("click", (e) => {
      e.preventDefault()
      e.stopPropagation()
      showView("welcome")
    })
  }

  if (navAutism) {
    navAutism.addEventListener("click", (e) => {
      e.preventDefault()
      e.stopPropagation()
      showView("autism-info")
    })
  }

  if (navInfo) {
    navInfo.addEventListener("click", (e) => {
      e.preventDefault()
      e.stopPropagation()
      showView("info")
    })
  }

  if (navToggle) {
    navToggle.addEventListener("click", (e) => {
      e.preventDefault()
      e.stopPropagation()
      toggleNavMenu()
    })
  }

  // Botones adicionales
  const autismInfoBtn = document.getElementById("autism-info-btn")
  if (autismInfoBtn) {
    autismInfoBtn.addEventListener("click", (e) => {
      e.preventDefault()
      showView("autism-info")
    })
  }

  const techInfoBtn = document.getElementById("tech-info-btn")
  if (techInfoBtn) {
    techInfoBtn.addEventListener("click", (e) => {
      e.preventDefault()
      showView("info")
    })
  }

  const backToWelcomeTechBtn = document.getElementById("back-to-welcome-tech-btn")
  if (backToWelcomeTechBtn) {
    backToWelcomeTechBtn.addEventListener("click", (e) => {
      e.preventDefault()
      showView("welcome")
    })
  }

  // Event listeners para botones del cuestionario
  document.addEventListener("click", (e) => {
    // Verificar si es un botón de navegación del cuestionario
    if (e.target.id === "prev-btn" || e.target.closest("#prev-btn")) {
      console.log("⬅️ Botón anterior clickeado")
      e.preventDefault()
      e.stopPropagation()
      previousStep()
      return
    }

    if (e.target.id === "next-btn" || e.target.closest("#next-btn")) {
      console.log("➡️ Botón siguiente clickeado")
      e.preventDefault()
      e.stopPropagation()
      nextStep()
      return
    }

    // Botón volver al inicio
    if (e.target.id === "back-to-welcome-btn" || e.target.closest("#back-to-welcome-btn")) {
      e.preventDefault()
      e.stopPropagation()
      showView("welcome")
      return
    }

    // Verificar si es una opción de radio
    const radioOption = e.target.closest(".radio-option")
    if (radioOption) {
      const questionId = radioOption.dataset.question
      const value = Number.parseInt(radioOption.dataset.value)
      if (questionId && value !== undefined) {
        console.log(`📝 Opción seleccionada: ${questionId} = ${value}`)
        e.preventDefault()
        e.stopPropagation()
        selectAnswer(questionId, value)
      }
    }
  })

  console.log("✅ Event listeners configurados")
}

// Navegación entre vistas
function showView(viewName) {
  console.log(`📱 Cambiando a vista: ${viewName}`)

  // Ocultar todas las vistas
  const views = document.querySelectorAll(".view")
  views.forEach((view) => view.classList.remove("active"))

  // Mostrar vista seleccionada
  const targetView = document.getElementById(`${viewName}-view`)
  if (targetView) {
    targetView.classList.add("active")
    console.log(`✅ Vista ${viewName} activada`)
  } else {
    console.error(`❌ Vista ${viewName}-view no encontrada`)
  }

  // Actualizar navegación
  updateNavigation(viewName)

  // Si es el cuestionario, inicializar
  if (viewName === "questionnaire") {
    console.log("🎯 Inicializando cuestionario...")
    setTimeout(() => {
      renderCurrentStep()
    }, 100)
  }
}

// Actualizar navegación activa
function updateNavigation(activeView) {
  const navButtons = document.querySelectorAll(".nav-btn")
  navButtons.forEach((btn) => btn.classList.remove("active"))

  const viewMap = {
    welcome: "nav-home",
    "autism-info": "nav-autism",
    info: "nav-info",
  }

  const activeNavId = viewMap[activeView]
  if (activeNavId) {
    const activeNav = document.getElementById(activeNavId)
    if (activeNav) {
      activeNav.classList.add("active")
    }
  }
}

// Toggle menú móvil
function toggleNavMenu() {
  const navMenu = document.getElementById("nav-menu")
  navMenu.classList.toggle("active")
}

// Iniciar evaluación
function startAssessment() {
  console.log("🚀 Iniciando evaluación...")

  currentStep = 0
  formData = {
    A1: null,
    A2: null,
    A3: null,
    A4: null,
    A5: null,
    A6: null,
    A7: null,
    A8: null,
    A9: null,
    A10: null,
    Age_Mons: null,
  }

  console.log("📊 Datos inicializados:", formData)
  console.log("📍 Paso actual:", currentStep)

  showView("questionnaire")
}

// Renderizar paso actual
function renderCurrentStep() {
  console.log(`🎨 Renderizando paso ${currentStep}`)

  const stepContent = document.getElementById("step-content")
  if (!stepContent) {
    console.error("❌ Elemento step-content no encontrado")
    return
  }

  const totalSteps = questions.length + 1 // +1 para edad
  const progress = (currentStep / totalSteps) * 100

  // Actualizar progreso
  const progressFill = document.getElementById("progress-fill")
  const progressText = document.getElementById("progress-text")

  if (progressFill) {
    progressFill.style.width = `${progress}%`
    console.log(`📊 Progreso actualizado: ${progress}%`)
  }
  if (progressText) {
    progressText.textContent = `${currentStep}/${totalSteps}`
  }

  if (currentStep === 0) {
    // Paso de edad
    console.log("👶 Renderizando paso de edad")
    stepContent.innerHTML = renderAgeStep()

    // Configurar listener para input de edad
    setTimeout(() => {
      const ageInput = document.getElementById("age-input")
      if (ageInput) {
        ageInput.addEventListener("input", handleAgeInput)
        console.log("✅ Listener de edad configurado")
      }
    }, 100)
  } else {
    // Pasos de preguntas
    const questionIndex = currentStep - 1
    const question = questions[questionIndex]
    console.log(`❓ Renderizando pregunta ${questionIndex + 1}:`, question.text)
    stepContent.innerHTML = renderQuestionStep(question, questionIndex + 1)
  }

  updateNavigationButtons()
}

// Renderizar paso de edad
function renderAgeStep() {
  return `
        <div class="step-content">
            <div class="age-input-section">
                <div class="age-input-header">
                    <i class="fas fa-calendar-alt"></i>
                    <h2 class="step-title">Edad del Niño/a</h2>
                </div>
                <p class="age-description">Ingrese la edad en meses (rango: 12-192 meses / 1-16 años)</p>
                
                <div class="age-input-group">
                    <label for="age-input" class="age-label">Edad en meses</label>
                    <input 
                        type="number" 
                        id="age-input" 
                        class="age-input"
                        min="12" 
                        max="192" 
                        placeholder="Ej: 24 (para 2 años)"
                        value="${formData.Age_Mons || ""}"
                    >
                    <div id="age-display" class="age-display" style="display: none;"></div>
                    <div id="age-error" class="age-error" style="display: none;">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span>La edad debe estar entre 12 y 192 meses (1-16 años)</span>
                    </div>
                </div>
            </div>
        </div>
    `
}

// Renderizar paso de pregunta
function renderQuestionStep(question, questionNumber) {
  const currentAnswer = formData[question.id]

  return `
        <div class="step-content">
            <div class="step-header">
                <h2 class="step-title">Pregunta ${questionNumber} de ${questions.length}</h2>
                <div class="step-category">${question.category}</div>
                ${question.critical ? '<div class="critical-badge"><i class="fas fa-exclamation-triangle"></i> Señal Crítica</div>' : ""}
            </div>
            
            <div class="question-card ${question.critical ? "critical" : ""}">
                <h3 class="question-text">${question.text}</h3>
                <p class="question-description">${question.description}</p>
                
                <div class="radio-group">
                    <label class="radio-option ${currentAnswer === 1 ? "selected" : ""}" data-question="${question.id}" data-value="1">
                        <input type="radio" name="question-answer" value="1" class="radio-input" ${currentAnswer === 1 ? "checked" : ""}>
                        <span class="radio-label">Sí - Esta conducta SÍ está presente o es frecuente</span>
                    </label>
                    <label class="radio-option ${currentAnswer === 0 ? "selected" : ""}" data-question="${question.id}" data-value="0">
                        <input type="radio" name="question-answer" value="0" class="radio-input" ${currentAnswer === 0 ? "checked" : ""}>
                        <span class="radio-label">No - Esta conducta NO está presente o es muy rara</span>
                    </label>
                </div>
            </div>
        </div>
    `
}

// Manejar input de edad
function handleAgeInput(event) {
  const age = Number.parseInt(event.target.value)
  console.log(`👶 Edad ingresada: ${age}`)

  const ageDisplay = document.getElementById("age-display")
  const ageError = document.getElementById("age-error")

  if (age >= 12 && age <= 192) {
    const years = Math.round((age / 12) * 10) / 10
    let ageGroup = ""

    if (age <= 36) ageGroup = " (bebé/niño pequeño)"
    else if (age <= 72) ageGroup = " (niño preescolar)"
    else if (age <= 144) ageGroup = " (niño escolar)"
    else ageGroup = " (adolescente)"

    if (ageDisplay) {
      ageDisplay.textContent = `${years} años${ageGroup}`
      ageDisplay.style.display = "block"
    }
    if (ageError) ageError.style.display = "none"

    formData.Age_Mons = age
    console.log(`✅ Edad válida guardada: ${age} meses`)
  } else if (age) {
    if (ageDisplay) ageDisplay.style.display = "none"
    if (ageError) ageError.style.display = "flex"
    formData.Age_Mons = null
    console.log(`❌ Edad inválida: ${age}`)
  } else {
    if (ageDisplay) ageDisplay.style.display = "none"
    if (ageError) ageError.style.display = "none"
    formData.Age_Mons = null
  }

  updateNavigationButtons()
}

// Seleccionar respuesta
function selectAnswer(questionId, value) {
  console.log(`📝 Respuesta seleccionada: ${questionId} = ${value}`)

  formData[questionId] = value

  // Debug: mostrar interpretación de la respuesta
  if (questionId !== "Age_Mons") {
    const isRiskAnswer = questionId === "A10" ? value === 1 : value === 0
    console.log(`   Interpretación: ${isRiskAnswer ? "SEÑAL DE RIESGO" : "RESPUESTA TÍPICA"}`)
  }

  // Actualizar UI
  const radioOptions = document.querySelectorAll(`input[name="question-answer"]`)
  radioOptions.forEach((radio) => {
    const option = radio.closest(".radio-option")
    option.classList.remove("selected")
    if (radio.value == value) {
      radio.checked = true
      option.classList.add("selected")
    }
  })

  console.log("📊 Estado actual formData:", formData)
  updateNavigationButtons()
}

// Verificar si se puede proceder
function checkCanProceed() {
  if (currentStep === 0) {
    const canProceed = formData.Age_Mons !== null && formData.Age_Mons >= 12 && formData.Age_Mons <= 192
    console.log(`🔍 Verificando edad: ${formData.Age_Mons}, puede proceder: ${canProceed}`)
    return canProceed
  } else {
    const questionIndex = currentStep - 1
    const question = questions[questionIndex]
    const canProceed = formData[question.id] !== null
    console.log(`🔍 Verificando pregunta ${question.id}: ${formData[question.id]}, puede proceder: ${canProceed}`)
    return canProceed
  }
}

// Actualizar botones de navegación
function updateNavigationButtons() {
  console.log("🔄 Actualizando botones de navegación...")

  const prevBtn = document.getElementById("prev-btn")
  const nextBtn = document.getElementById("next-btn")

  if (!prevBtn || !nextBtn) {
    console.error("❌ Botones de navegación no encontrados")
    return
  }

  // Botón anterior
  const canGoBack = currentStep > 0
  prevBtn.disabled = !canGoBack
  console.log(`⬅️ Botón anterior: ${canGoBack ? "habilitado" : "deshabilitado"}`)

  // Botón siguiente
  const canProceed = checkCanProceed()
  nextBtn.disabled = !canProceed

  if (currentStep === questions.length) {
    nextBtn.innerHTML = '<i class="fas fa-brain"></i> Analizar Resultados'
  } else {
    nextBtn.innerHTML = "Siguiente"
  }

  console.log(`➡️ Botón siguiente: ${canProceed ? "habilitado" : "deshabilitado"}`)
}

// Paso anterior
function previousStep() {
  console.log(`⬅️ Intentando ir al paso anterior desde ${currentStep}`)

  if (currentStep > 0) {
    currentStep--
    console.log(`✅ Paso anterior: ${currentStep}`)
    renderCurrentStep()
  } else {
    console.log("❌ Ya está en el primer paso")
  }
}

// Paso siguiente
async function nextStep() {
  console.log(`➡️ Intentando ir al paso siguiente desde ${currentStep}`)

  if (currentStep < questions.length) {
    currentStep++
    console.log(`✅ Paso siguiente: ${currentStep}`)
    renderCurrentStep()
  } else {
    console.log("🧠 Iniciando análisis de resultados...")
    await analyzeResults()
  }
}

// Analizar resultados con clustering completo
async function analyzeResults() {
  console.log("🔬 Iniciando análisis con clustering completo...")
  showView("analysis")

  try {
    // Simular tiempo de análisis
    await new Promise((resolve) => setTimeout(resolve, 3000))

    // Intentar usar backend Python completo
    try {
      console.log("🐍 Intentando conectar con backend Python completo...")

      // Agregar K por defecto (3 clases)
      const requestData = { ...formData, selected_k: 3 }

      const response = await fetch(`${API_BASE_URL}/predict`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestData),
      })

      if (response.ok) {
        analysisResult = await response.json()
        console.log("✅ Análisis completado con backend Python completo")

        // Extraer análisis de clustering si está disponible
        if (analysisResult.clustering_analysis && analysisResult.clustering_analysis.success) {
          clusteringAnalysis = analysisResult.clustering_analysis
          console.log("📈 Análisis de clustering completo disponible")
          console.log("🖼️ Visualización del codo:", clusteringAnalysis.elbow_plot ? "Disponible" : "No disponible")
          console.log(
            "🖼️ Visualización clustering:",
            clusteringAnalysis.clustering_plot ? "Disponible" : "No disponible",
          )
          console.log("📂 Fuente de datos:", clusteringAnalysis.data_source || "Desconocida")
          console.log("🎯 K seleccionado:", clusteringAnalysis.selected_k)
          console.log("💡 K óptimo sugerido:", clusteringAnalysis.optimal_k_suggested)
        }
      } else {
        throw new Error("Error en backend")
      }
    } catch (error) {
      console.warn("⚠️ Backend no disponible, usando análisis local")
      analysisResult = analyzeLocally(formData)
    }

    showResults()
  } catch (error) {
    console.error("❌ Error en análisis:", error)
    alert("Error al procesar la evaluación. Por favor, intente nuevamente.")
    showView("questionnaire")
  }
}

// Análisis local (fallback)
function analyzeLocally(data) {
  console.log("🏠 Ejecutando análisis local...")

  const age = data.Age_Mons

  // Calcular puntuación de riesgo corregida
  let correctedRiskScore = 0
  for (let i = 1; i <= 9; i++) {
    const col = `A${i}`
    const response = data[col]
    if (response !== null) {
      // Para A1-A9: No (0) = riesgo, Sí (1) = típico
      correctedRiskScore += 1 - response
    }
  }
  // Para A10: Sí (1) = riesgo, No (0) = típico
  if (data.A10 !== null) {
    correctedRiskScore += data.A10
  }

  console.log(`📊 Score de riesgo calculado: ${correctedRiskScore}/10`)

  // Determinar grupo de edad
  let ageGroup = ""
  if (age <= 36) ageGroup = "bebé/niño pequeño"
  else if (age <= 72) ageGroup = "niño preescolar"
  else if (age <= 144) ageGroup = "niño escolar"
  else ageGroup = "adolescente"

  // Clasificación basada en score real
  let alertLevel, riskRange, mainMessage, recommendation

  if (correctedRiskScore >= 7) {
    alertLevel = "ALTO RIESGO"
    riskRange = "70-85%"
    mainMessage = `Su ${ageGroup} muestra ${correctedRiskScore} señales de riesgo significativas.`
    recommendation = "🚨 URGENTE: Evaluación profesional en 1-2 semanas"
  } else if (correctedRiskScore >= 4) {
    alertLevel = "RIESGO MODERADO"
    riskRange = "25-45%"
    mainMessage = `Su ${ageGroup} muestra ${correctedRiskScore} señales de riesgo que requieren atención.`
    recommendation = "⚠️ IMPORTANTE: Consulta con pediatra en 2-4 semanas"
  } else {
    alertLevel = "BAJO RIESGO"
    riskRange = "5-15%"
    mainMessage = `Su ${ageGroup} muestra ${correctedRiskScore} señales de riesgo mínimas.`
    recommendation = "✅ TRANQUILIDAD: Continúe con controles regulares"
  }

  return {
    corrected_risk_score: correctedRiskScore,
    total_signals: 10,
    age_months: age,
    age_years: Math.round((age / 12) * 10) / 10,
    age_group: ageGroup,
    alert_level: alertLevel,
    risk_range: riskRange,
    main_message: mainMessage,
    recommendation: recommendation,
    important_note:
      "⚠️ IMPORTANTE: Este sistema NO diagnostica autismo. Solo un profesional puede realizar un diagnóstico adecuado.",
    is_atypical: false,
    clinical_risk_index: correctedRiskScore / 10.0,
    confidence_score: 85,
    selected_k: 3,
  }
}

// Mostrar resultados completos
function showResults() {
  console.log("📊 Mostrando resultados completos...")
  showView("results")
  const resultsContent = document.getElementById("results-content")

  if (!resultsContent) {
    console.error("❌ Elemento results-content no encontrado")
    return
  }

  let clusteringSection = ""
  let visualizationsSection = ""

  // Agregar sección de clustering completa si está disponible
  if (clusteringAnalysis && clusteringAnalysis.cluster_report) {
    const report = clusteringAnalysis.cluster_report

    // Sección de visualizaciones completa
    visualizationsSection = `
      <!-- Visualizaciones de Clustering Completas -->
      <div class="visualizations-card">
        <div class="visualizations-header">
          <i class="fas fa-chart-line"></i>
          <h3>Análisis de Clustering Avanzado con Combobox</h3>
        </div>
        <div class="visualizations-content">
          <!-- Selector de K Completo -->
          <div class="k-selector-section">
            <h4>🎯 Configuración de Grupos de Riesgo:</h4>
            <div class="k-selector">
              <label for="k-select">Número de Clases de Riesgo:</label>
              <select id="k-select" onchange="updateClusteringK()" class="k-select-enhanced">
                <option value="2" ${clusteringAnalysis.selected_k === 2 ? "selected" : ""}>
                  K=2 - CON AUTISMO vs SIN AUTISMO (Binario)
                </option>
                <option value="3" ${clusteringAnalysis.selected_k === 3 ? "selected" : ""}>
                  K=3 - BAJO | MODERADO | ALTO RIESGO (Tres Niveles)
                </option>
              </select>
              <div class="k-info-enhanced">
                <span class="k-optimal">K óptimo sugerido: ${clusteringAnalysis.optimal_k_suggested || 3}</span>
                <span class="k-current">K actual: ${clusteringAnalysis.selected_k}</span>
              </div>
            </div>
            <div class="k-explanation">
              ${
                clusteringAnalysis.selected_k === 2
                  ? '<p><strong>K=2:</strong> Clasificación binaria que divide los casos en <span class="highlight-green">SIN AUTISMO</span> y <span class="highlight-red">CON AUTISMO</span>.</p>'
                  : '<p><strong>K=3:</strong> Clasificación en tres niveles: <span class="highlight-green">BAJO RIESGO</span>, <span class="highlight-orange">RIESGO MODERADO</span> y <span class="highlight-red">ALTO RIESGO</span>.</p>'
              }
            </div>
          </div>
          
          <!-- Método del Codo -->
          ${
            clusteringAnalysis.elbow_plot
              ? `
          <div class="visualization-item">
            <h4>📈 Método del Codo - Determinación de K Óptimo</h4>
            <div class="plot-container">
              <img src="${clusteringAnalysis.elbow_plot}" alt="Método del Codo" class="clustering-plot-image">
            </div>
            <p class="plot-description">
              El método del codo ayuda a determinar el número óptimo de clusters. 
              El punto donde la curva forma un "codo" indica el K más eficiente para el análisis.
            </p>
          </div>
          `
              : ""
          }
          
          <!-- Clustering Principal Completo -->
          ${
            clusteringAnalysis.clustering_plot
              ? `
          <div class="visualization-item enhanced">
            <h4>🎯 Clustering de Grupos de Riesgo (K=${clusteringAnalysis.selected_k})</h4>
            <div class="plot-container enhanced">
              <img src="${clusteringAnalysis.clustering_plot}" alt="Clustering de Riesgo" class="clustering-plot-image">
            </div>
            <div class="plot-description enhanced">
              <p><strong>Su hijo/a está representado por la ⭐ amarilla.</strong></p>
              ${
                clusteringAnalysis.selected_k === 2
                  ? '<p>Los colores indican: <span class="color-green">Verde = Sin Autismo (Bajo Riesgo)</span> | <span class="color-red">Rojo = Con Autismo (Alto Riesgo)</span></p>'
                  : '<p>Los colores indican: <span class="color-green">Verde = Bajo Riesgo</span> | <span class="color-orange">Naranja = Riesgo Moderado</span> | <span class="color-red">Rojo = Alto Riesgo</span></p>'
              }
              <p>Su posición relativa muestra la similitud con otros casos históricos del dataset.</p>
            </div>
          </div>
          `
              : ""
          }
          
          <!-- Información del Algoritmo Completa -->
          <div class="algorithm-info enhanced">
            <h4>🔧 Información Técnica del Análisis:</h4>
            <div class="algorithm-details">
              <div class="detail-item">
                <span class="detail-label">Algoritmo Usado:</span>
                <span class="detail-value">${clusteringAnalysis.selected_algorithm || "K-means"}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">K Seleccionado:</span>
                <span class="detail-value">${clusteringAnalysis.selected_k} ${clusteringAnalysis.selected_k === 2 ? "(Binario)" : "(Tres Niveles)"}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">K Óptimo Sugerido:</span>
                <span class="detail-value">${clusteringAnalysis.optimal_k_suggested || "No disponible"}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">Casos Históricos:</span>
                <span class="detail-value">${clusteringAnalysis.total_cases || "No disponible"}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">Fuente de Datos:</span>
                <span class="detail-value">${clusteringAnalysis.data_source || "Datos de ejemplo"}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    `

    // Sección de análisis de clustering completa
    clusteringSection = `
      <!-- Análisis de Clustering Completo -->
      <div class="clustering-analysis-card enhanced">
        <div class="clustering-header">
          <i class="fas fa-chart-scatter"></i>
          <h3>Análisis de Clustering: ${report.cluster_id}</h3>
        </div>
        <div class="clustering-content">
          <div class="cluster-assignment enhanced">
            <h4>Grupo Asignado:</h4>
            <div class="cluster-badge ${report.risk_color} enhanced">
              ${report.risk_level}
            </div>
            <div class="position-info">
              <p><strong>Posición:</strong> ${report.position_description}</p>
            </div>
          </div>
          
          <div class="cluster-interpretation enhanced">
            <h4>Interpretación del Grupo:</h4>
            <p>${report.interpretation}</p>
          </div>
          
          <div class="cluster-characteristics enhanced">
            <h4>Características Principales del Grupo:</h4>
            <ul class="characteristics-list">
              ${report.main_characteristics.map((char) => `<li>${char}</li>`).join("")}
            </ul>
          </div>
          
          <div class="cluster-stats enhanced">
            <div class="stat-item">
              <span class="stat-label">Tamaño del Grupo:</span>
              <span class="stat-value">${report.cluster_size} casos (${report.cluster_percentage.toFixed(1)}%)</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Probabilidad ASD:</span>
              <span class="stat-value">${report.asd_probability}</span>
            </div>
          </div>
          
          <div class="visual-explanation enhanced">
            <h4>Explicación Visual:</h4>
            <p>${report.visual_position}</p>
          </div>
          
          <div class="algorithm-explanation enhanced">
            <h4>Información del Algoritmo:</h4>
            <p>${report.algorithm_info}</p>
          </div>
        </div>
      </div>
    `
  }

  resultsContent.innerHTML = `
        <!-- Información del análisis -->
        <div class="analysis-info enhanced">
            <div class="analysis-info-header">
                <i class="fas fa-info-circle"></i>
                <h3>Información del Análisis</h3>
            </div>
            <div class="analysis-info-grid">
                <div class="info-item">
                    <span class="info-label">Edad:</span>
                    <span class="info-value">${analysisResult.age_years} años (${analysisResult.age_months} meses)</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Grupo:</span>
                    <span class="info-value">${analysisResult.age_group}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Señales de Riesgo:</span>
                    <span class="info-value">${analysisResult.corrected_risk_score}/${analysisResult.total_signals}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Confianza:</span>
                    <span class="info-value">${analysisResult.confidence_score}%</span>
                </div>
            </div>
        </div>

        <!-- Nivel de alerta -->
        <div class="alert-card ${getAlertClass(analysisResult.alert_level)} enhanced">
            <div class="alert-header">
                <span class="alert-level">${analysisResult.alert_level}</span>
                <span class="alert-range">Tasa ASD: ${analysisResult.risk_range}</span>
            </div>
        </div>

        <!-- Mensaje principal -->
        <div class="main-message-card enhanced">
            <div class="message-header">
                <i class="fas fa-heart"></i>
                <h3>Mensaje para los Padres</h3>
            </div>
            <p class="message-text">${analysisResult.main_message}</p>
            <div class="recommendation-box">
                <p class="recommendation-text">${analysisResult.recommendation}</p>
            </div>
        </div>

        ${visualizationsSection}

        ${clusteringSection}

        <!-- Nota importante -->
        <div class="important-note enhanced">
            <i class="fas fa-exclamation-triangle"></i>
            <div class="important-note-text">${analysisResult.important_note}</div>
        </div>

        <!-- Botones de acción completos -->
        <div class="results-actions enhanced">
            <button class="btn btn-outline" onclick="resetAssessment()">
                <i class="fas fa-redo"></i>
                Realizar Nueva Evaluación
            </button>
            <button class="btn btn-primary" onclick="window.print()">
                <i class="fas fa-print"></i>
                Imprimir Resultados
            </button>
            ${
              clusteringAnalysis
                ? `
            <button class="btn btn-secondary" onclick="showClusteringDetails()">
                <i class="fas fa-chart-line"></i>
                Ver Análisis Detallado
            </button>
            <button class="btn btn-secondary" onclick="downloadClusteringData()">
                <i class="fas fa-download"></i>
                Descargar Datos
            </button>
            <button class="btn btn-info" onclick="explainClustering()">
                <i class="fas fa-question-circle"></i>
                ¿Cómo Funciona?
            </button>
            `
                : ""
            }
        </div>
    `
}

// Función completa para actualizar el clustering con nuevo K
async function updateClusteringK() {
  const kSelect = document.getElementById("k-select")
  const selectedK = Number.parseInt(kSelect.value)

  console.log(`🔄 Actualizando clustering con K=${selectedK}`)

  // Mostrar indicador de carga
  const visualizationsContent = document.querySelector(".visualizations-content")
  if (visualizationsContent) {
    visualizationsContent.style.opacity = "0.5"

    // Agregar spinner de carga
    const loadingDiv = document.createElement("div")
    loadingDiv.className = "loading-overlay"
    loadingDiv.innerHTML = `
      <div class="loading-spinner">
        <i class="fas fa-spinner fa-spin"></i>
        <p>Actualizando clustering con K=${selectedK}...</p>
      </div>
    `
    visualizationsContent.appendChild(loadingDiv)
  }

  try {
    // Preparar datos con nuevo K
    const requestData = { ...formData, selected_k: selectedK }

    const response = await fetch(`${API_BASE_URL}/predict`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(requestData),
    })

    if (response.ok) {
      const newResult = await response.json()

      // Actualizar variables globales
      analysisResult = newResult
      clusteringAnalysis = newResult.clustering_analysis

      // Volver a renderizar resultados
      showResults()

      console.log(`✅ Clustering actualizado con K=${selectedK}`)

      // Mostrar notificación de éxito
      showNotification(`Clustering actualizado exitosamente con K=${selectedK}`, "success")
    } else {
      console.error("❌ Error al actualizar clustering")
      showNotification("Error al actualizar el análisis de clustering", "error")
    }
  } catch (error) {
    console.error("❌ Error en actualización:", error)
    showNotification("Error de conexión al actualizar clustering", "error")
  } finally {
    // Restaurar opacidad y remover spinner
    if (visualizationsContent) {
      visualizationsContent.style.opacity = "1"
      const loadingOverlay = visualizationsContent.querySelector(".loading-overlay")
      if (loadingOverlay) {
        loadingOverlay.remove()
      }
    }
  }
}

// Función para mostrar notificaciones
function showNotification(message, type = "info") {
  const notification = document.createElement("div")
  notification.className = `notification ${type}`
  notification.innerHTML = `
    <i class="fas fa-${type === "success" ? "check-circle" : type === "error" ? "exclamation-circle" : "info-circle"}"></i>
    <span>${message}</span>
  `

  document.body.appendChild(notification)

  // Auto-remover después de 3 segundos
  setTimeout(() => {
    notification.remove()
  }, 3000)
}

// Función completa para descargar datos de clustering
function downloadClusteringData() {
  if (clusteringAnalysis) {
    const data = {
      cluster_report: clusteringAnalysis.cluster_report,
      selected_k: clusteringAnalysis.selected_k,
      optimal_k_suggested: clusteringAnalysis.optimal_k_suggested,
      total_cases: clusteringAnalysis.total_cases,
      data_source: clusteringAnalysis.data_source,
      user_responses: formData,
      analysis_timestamp: new Date().toISOString(),
      system_version: "Completo v3.0",
    }

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: "application/json" })
    const url = URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = `clustering_analysis_k${clusteringAnalysis.selected_k}_${new Date().toISOString().split("T")[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    console.log("📥 Datos de clustering descargados")
    showNotification("Datos de clustering descargados exitosamente", "success")
  }
}

// Función completa para mostrar detalles de clustering
function showClusteringDetails() {
  if (clusteringAnalysis) {
    const details = `
DETALLES DEL ANÁLISIS DE CLUSTERING COMPLETO

═══════════════════════════════════════════════

🎯 CONFIGURACIÓN:
• Cluster Asignado: ${clusteringAnalysis.cluster_report.cluster_id}
• Nivel de Riesgo: ${clusteringAnalysis.cluster_report.risk_level}
• K Seleccionado: ${clusteringAnalysis.selected_k} ${clusteringAnalysis.selected_k === 2 ? "(Binario: Con/Sin Autismo)" : "(Tres Niveles: Bajo/Moderado/Alto)"}
• K Óptimo Sugerido: ${clusteringAnalysis.optimal_k_suggested || "No disponible"}

📊 ESTADÍSTICAS DEL GRUPO:
• Tamaño del Cluster: ${clusteringAnalysis.cluster_report.cluster_size} casos
• Porcentaje del Total: ${clusteringAnalysis.cluster_report.cluster_percentage.toFixed(1)}%
• Probabilidad ASD: ${clusteringAnalysis.cluster_report.asd_probability}

🔍 CARACTERÍSTICAS PRINCIPALES:
${clusteringAnalysis.cluster_report.main_characteristics.map((char) => `• ${char}`).join("\n")}

📍 POSICIÓN EN EL GRÁFICO:
${clusteringAnalysis.cluster_report.position_description}

💡 INTERPRETACIÓN:
${clusteringAnalysis.cluster_report.interpretation}

🔧 INFORMACIÓN TÉCNICA:
• Algoritmo: ${clusteringAnalysis.cluster_report.algorithm_info}
• Fuente de datos: ${clusteringAnalysis.data_source || "Datos de ejemplo"}
• Total de casos analizados: ${clusteringAnalysis.total_cases || "No disponible"}

═══════════════════════════════════════════════
Sistema de Clustering Completo v3.0
    `

    // Crear modal personalizado para mostrar detalles
    const modal = document.createElement("div")
    modal.className = "details-modal"
    modal.innerHTML = `
      <div class="modal-content">
        <div class="modal-header">
          <h3><i class="fas fa-chart-scatter"></i> Detalles del Clustering</h3>
          <button class="modal-close" onclick="this.parentElement.parentElement.parentElement.remove()">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-body">
          <pre>${details}</pre>
        </div>
        <div class="modal-footer">
          <button class="btn btn-outline" onclick="this.parentElement.parentElement.parentElement.remove()">
            Cerrar
          </button>
          <button class="btn btn-primary" onclick="navigator.clipboard.writeText('${details.replace(/'/g, "\\'")}'); showNotification('Detalles copiados al portapapeles', 'success')">
            <i class="fas fa-copy"></i> Copiar
          </button>
        </div>
      </div>
    `

    document.body.appendChild(modal)
  }
}

// Nueva función completa para explicar el clustering
function explainClustering() {
  const explanation = `
¿CÓMO FUNCIONA EL CLUSTERING EN ESTE SISTEMA?

═══════════════════════════════════════════════

🧠 ¿QUÉ ES EL CLUSTERING?
El clustering es una técnica de inteligencia artificial que agrupa casos similares basándose en sus características. En este sistema, analizamos las respuestas del cuestionario Q-CHAT-10 para identificar patrones de riesgo de autismo.

🎯 COMBOBOX DE CLASES (K=2 vs K=3):

📊 K=2 (CLASIFICACIÓN BINARIA):
• Divide los casos en DOS grupos principales
• Verde: SIN AUTISMO - Bajo riesgo
• Rojo: CON AUTISMO - Alto riesgo
• Útil para decisiones binarias simples

📊 K=3 (CLASIFICACIÓN DE TRES NIVELES):
• Divide los casos en TRES grupos de riesgo
• Verde: BAJO RIESGO
• Naranja: RIESGO MODERADO/INTERMEDIO
• Rojo: ALTO RIESGO
• Proporciona más matices en la evaluación

⭐ SU POSICIÓN:
La estrella amarilla muestra dónde se ubica su hijo/a en relación con ${clusteringAnalysis?.total_cases || "cientos de"} casos históricos. Su posición indica qué tan similar es su patrón de respuestas a otros casos.

📈 MÉTODO DEL CODO:
Determina automáticamente el número óptimo de grupos analizando la calidad del clustering. El "codo" en la gráfica indica el punto de equilibrio entre simplicidad y precisión.

🔬 ALGORITMO K-MEANS:
• Agrupa casos con características similares
• Minimiza las diferencias dentro de cada grupo
• Maximiza las diferencias entre grupos
• Proporciona interpretación clínica clara

⚕️ INTERPRETACIÓN CLÍNICA:
Cada grupo tiene características específicas que ayudan a los profesionales a entender el perfil de riesgo y proporcionar recomendaciones apropiadas.

═══════════════════════════════════════════════
IMPORTANTE: Este es un sistema de apoyo, no un diagnóstico médico.
  `

  // Crear modal para la explicación
  const modal = document.createElement("div")
  modal.className = "explanation-modal"
  modal.innerHTML = `
    <div class="modal-content large">
      <div class="modal-header">
        <h3><i class="fas fa-lightbulb"></i> ¿Cómo Funciona el Clustering?</h3>
        <button class="modal-close" onclick="this.parentElement.parentElement.parentElement.remove()">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="modal-body">
        <pre>${explanation}</pre>
      </div>
      <div class="modal-footer">
        <button class="btn btn-outline" onclick="this.parentElement.parentElement.parentElement.remove()">
          Cerrar
        </button>
        <button class="btn btn-info" onclick="window.open('https://es.wikipedia.org/wiki/Análisis_de_clúster', '_blank')">
          <i class="fas fa-external-link-alt"></i> Más Info
        </button>
      </div>
    </div>
  `

  document.body.appendChild(modal)
}

// Obtener clase CSS para alerta
function getAlertClass(alertLevel) {
  switch (alertLevel) {
    case "BAJO RIESGO":
    case "BAJO RIESGO (SIN AUTISMO)":
    case "SIN AUTISMO (BAJO RIESGO)":
      return "low-risk"
    case "RIESGO MODERADO":
    case "RIESGO MODERADO/INTERMEDIO":
      return "moderate-risk"
    case "ALTO RIESGO":
    case "ALTO RIESGO (CON AUTISMO)":
    case "CON AUTISMO (ALTO RIESGO)":
      return "high-risk"
    case "EVALUACIÓN ESPECIALIZADA":
      return "atypical"
    default:
      return "low-risk"
  }
}

// Reiniciar evaluación
function resetAssessment() {
  console.log("🔄 Reiniciando evaluación...")

  currentStep = 0
  formData = {
    A1: null,
    A2: null,
    A3: null,
    A4: null,
    A5: null,
    A6: null,
    A7: null,
    A8: null,
    A9: null,
    A10: null,
    Age_Mons: null,
  }
  analysisResult = null
  clusteringAnalysis = null
  showView("welcome")
}

// Log de inicialización completo
console.log("🚀 Sistema Completo de Evaluación de Autismo con Clustering cargado")
console.log("🔧 Funcionalidades completas disponibles:")
console.log("   • Cuestionario Q-CHAT-10 adaptado")
console.log("   • Combobox para seleccionar K=2 o K=3")
console.log("   • K=2: Clasificación binaria CON/SIN AUTISMO")
console.log("   • K=3: Tres niveles BAJO/MODERADO/ALTO RIESGO")
console.log("   • Visualizaciones con posicionamiento claro")
console.log("   • Estrella amarilla marca posición del usuario")
console.log("   • Interpretación específica por número de clases")
console.log("   • Método del codo para K óptimo")
console.log("   • Análisis detallado y explicaciones")
console.log("   • Rango de edad extendido (1-16 años)")
console.log("   • Soporte para CSV local completo")
console.log("   • Debugging completo habilitado")
console.log("   • Interfaz web responsive y moderna")

// Función para abrir página de gráficos
function abrirGraficos() {
  console.log("🔗 Abriendo página de gráficos...")

  // Construir URL de gráficos usando el servidor actual
  const graficosUrl = `${API_BASE_URL}/graficos`

  console.log(`📊 URL de gráficos: ${graficosUrl}`)

  // Abrir en nueva pestaña
  window.open(graficosUrl, '_blank')
}

// Hacer formData disponible globalmente para clustering_ui.js
window.formData = formData
