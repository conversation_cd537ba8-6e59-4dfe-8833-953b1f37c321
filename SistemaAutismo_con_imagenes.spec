# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('datos.csv', '.'),
        ('index.html', '.'),
        ('styles.css', '.'),
        ('script.js', '.'),
        ('PaginaGraficos/index.html', 'PaginaGraficos'),
        ('PaginaGraficos/graficos_generados/01_eda_distribuciones_numericas.png', 'PaginaGraficos/graficos_generados'),
        ('PaginaGraficos/graficos_generados/02_eda_variables_categoricas.png', 'PaginaGraficos/graficos_generados'),
        ('PaginaGraficos/graficos_generados/03_eda_matriz_correlacion.png', 'PaginaGraficos/graficos_generados'),
        ('PaginaGraficos/graficos_generados/04_eda_analisis_grupos_riesgo.png', 'PaginaGraficos/graficos_generados'),
        ('PaginaGraficos/graficos_generados/05_clustering_inicial_pca.png', 'PaginaGraficos/graficos_generados'),
        ('PaginaGraficos/graficos_generados/06_clustering_inicial_tsne.png', 'PaginaGraficos/graficos_generados'),
        ('PaginaGraficos/graficos_generados/07_comparacion_antes_despues.png', 'PaginaGraficos/graficos_generados'),
        ('PaginaGraficos/graficos_generados/08_importancia_caracteristicas.png', 'PaginaGraficos/graficos_generados'),
        ('PaginaGraficos/graficos_generados/09_arboles_decision_comparacion.png', 'PaginaGraficos/graficos_generados'),
        ('PaginaGraficos/graficos_generados/10_analisis_clusters_final.png', 'PaginaGraficos/graficos_generados')
    ],
    hiddenimports=[
        'flask',
        'pandas',
        'numpy',
        'scikit-learn',
        'matplotlib',
        'seaborn',
        'sklearn.cluster',
        'sklearn.preprocessing',
        'sklearn.decomposition',
        'sklearn.metrics',
        'sklearn.neighbors'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='SistemaAutismo',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    cofile=None,
    icon=None,
)
