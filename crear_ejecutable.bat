@echo off
echo ==========================================
echo CREADOR DE EJECUTABLE - SISTEMA AUTISMO
echo ==========================================
echo Version: Hibrido Completo DBSCAN + GMM + ML
echo Edades: 1-16 anos (12-192 meses)
echo CON CORRECCIONES DE CONEXION APLICADAS
echo ==========================================
echo.

echo [1/7] Verificando Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python no esta instalado o no esta en el PATH
    echo Por favor instale Python 3.8 o superior
    echo Descarga: https://www.python.org/downloads/
    pause
    exit /b 1
)

python --version
echo Python encontrado correctamente
echo.

echo [2/7] Verificando correcciones aplicadas...
echo Ejecutando verificacion basica de correcciones...
python test_basico.py
if errorlevel 1 (
    echo ERROR: Las correcciones no estan aplicadas correctamente
    echo Ejecute primero las correcciones necesarias
    pause
    exit /b 1
)
echo Correcciones verificadas correctamente
echo.

echo [3/7] Instalando dependencias...
python instalar_dependencias.py
if errorlevel 1 (
    echo ERROR: Fallo la instalacion de dependencias
    echo Verifique su conexion a internet
    pause
    exit /b 1
)

echo.
echo [4/7] Probando secuencia de ejecucion...
if exist "test_secuencia.py" (
    python test_secuencia.py
    if errorlevel 1 (
        echo ERROR: La secuencia clustering_visualizer - app - index.html fallo
        echo Revise los errores mostrados arriba
        pause
        exit /b 1
    )
) else (
    echo test_secuencia.py no encontrado, saltando...
)

echo.
echo [5/7] Ejecutando pruebas del sistema corregido...
if exist "test_ejecutable.py" (
    python test_ejecutable.py
    if errorlevel 1 (
        echo ERROR: Las pruebas del sistema fallaron
        echo Revise los errores mostrados arriba
        pause
        exit /b 1
    )
) else (
    echo test_ejecutable.py no encontrado, usando test rapido...
    python test_rapido_simple.py
    if errorlevel 1 (
        echo ERROR: Las pruebas rapidas fallaron
        echo Revise los errores mostrados arriba
        pause
        exit /b 1
    )
)

echo.
echo [6/7] Construyendo ejecutable con correcciones...
python build_exe.py
if errorlevel 1 (
    echo ERROR: Fallo la construccion del ejecutable
    echo Revise los errores mostrados arriba
    pause
    exit /b 1
)

echo.
echo [7/7] Verificando ejecutable creado...
if exist "dist\SistemaAutismo.exe" (
    echo Ejecutable encontrado correctamente
    for %%A in ("dist\SistemaAutismo.exe") do echo Tamano: %%~zA bytes
) else (
    echo ERROR: El ejecutable no fue creado
    pause
    exit /b 1
)

echo.
echo ==========================================
echo EJECUTABLE CREADO EXITOSAMENTE
echo ==========================================
echo Ubicacion: dist\SistemaAutismo.exe
echo.
echo CORRECCIONES INCLUIDAS:
echo - Inicializacion correcta del atributo 'data'
echo - Deteccion automatica de puerto en JavaScript
echo - Manejo de errores robusto
echo - Endpoint de test de conexion
echo - Sistema de respaldo en caso de fallos
echo.
echo Caracteristicas:
echo - Aplicacion independiente (no requiere Python)
echo - Servidor Flask integrado
echo - Abre navegador automaticamente
echo - Clustering interactivo DBSCAN + GMM + ML
echo - Analisis para edades 1-16 anos
echo - SIN ERRORES EN BUCLE DE CONEXION
echo.
echo El ejecutable esta listo para distribuir!
echo DEBERIA FUNCIONAR SIN PROBLEMAS DE CONEXION
echo.
pause
