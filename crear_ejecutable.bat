@echo off
echo ==========================================
echo CREADOR DE EJECUTABLE - SISTEMA AUTISMO
echo ==========================================
echo Version: Hibrido Completo DBSCAN + GMM + ML
echo Edades: 1-16 anos (12-192 meses)
echo ==========================================
echo.

echo [1/5] Verificando Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python no esta instalado o no esta en el PATH
    echo Por favor instale Python 3.8 o superior
    echo Descarga: https://www.python.org/downloads/
    pause
    exit /b 1
)

python --version
echo Python encontrado correctamente
echo.

echo [2/5] Instalando dependencias...
python instalar_dependencias.py
if errorlevel 1 (
    echo ERROR: Fallo la instalacion de dependencias
    echo Verifique su conexion a internet
    pause
    exit /b 1
)

echo.
echo [3/5] Ejecutando pruebas del sistema...
python test_ejecutable.py
if errorlevel 1 (
    echo ERROR: Las pruebas del sistema fallaron
    echo Revise los errores mostrados arriba
    pause
    exit /b 1
)

echo.
echo [4/5] Construyendo ejecutable...
python build_exe.py
if errorlevel 1 (
    echo ERROR: Fallo la construccion del ejecutable
    echo Revise los errores mostrados arriba
    pause
    exit /b 1
)

echo.
echo [5/5] Verificando ejecutable creado...
if exist "dist\SistemaAutismo.exe" (
    echo Ejecutable encontrado correctamente
    for %%A in ("dist\SistemaAutismo.exe") do echo Tamano: %%~zA bytes
) else (
    echo ERROR: El ejecutable no fue creado
    pause
    exit /b 1
)

echo.
echo ==========================================
echo EJECUTABLE CREADO EXITOSAMENTE
echo ==========================================
echo Ubicacion: dist\SistemaAutismo.exe
echo.
echo Caracteristicas:
echo - Aplicacion independiente (no requiere Python)
echo - Servidor Flask integrado
echo - Abre navegador automaticamente
echo - Clustering interactivo DBSCAN + GMM + ML
echo - Analisis para edades 1-16 anos
echo.
echo El ejecutable esta listo para distribuir!
echo.
pause
