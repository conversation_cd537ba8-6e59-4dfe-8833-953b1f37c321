@echo off
==========================================
echo CREADOR DE EJECUTABLE - SISTEMA AUTISMO
echo ==========================================
echo Version: Hibrido Completo DBSCAN + GMM + ML
echo Edades: 1-16 anos (12-192 meses)
echo SCRIPT FUSIONADO CON TODAS LAS CORRECCIONES
echo ==========================================
echo.

echo [1/6] Verificando Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python no esta instalado o no esta en el PATH
    echo Por favor instale Python 3.8 o superior
    echo Descarga: https://www.python.org/downloads/
    pause
    exit /b 1
)

python --version
echo Python encontrado correctamente
echo.

echo [2/6] Verificando script de construccion fusionado...
echo Verificando que build_exe_final.py existe...
if exist "build_exe_final.py" (
    echo ✅ Script fusionado encontrado
    echo Este script contiene todas las correcciones y verificaciones
) else (
    echo ERROR: build_exe_final.py no encontrado
    echo Este script fusionado es necesario para la construccion
    pause
    exit /b 1
)
echo Script de construccion verificado
echo.

echo [3/6] Instalando dependencias...
python instalar_dependencias.py
if errorlevel 1 (
    echo ERROR: Fallo la instalacion de dependencias
    echo Verifique su conexion a internet
    pause
    exit /b 1
)

echo.
echo [4/6] El script fusionado incluye todas las verificaciones...
echo Las verificaciones de archivos, correcciones y dependencias
echo se ejecutaran automaticamente en build_exe_final.py
echo Saltando pruebas separadas...

echo.
echo [5/6] Construyendo ejecutable con script fusionado...
echo Ejecutando build_exe_final.py (incluye todas las correcciones)...
python build_exe_final.py
if errorlevel 1 (
    echo ERROR: Fallo la construccion del ejecutable
    echo El script fusionado incluye manejo robusto de errores
    echo Revise los errores mostrados arriba
    pause
    exit /b 1
)

echo.
echo [6/6] Verificando ejecutable creado...
if exist "dist\SistemaAutismo.exe" (
    echo ✅ Ejecutable encontrado correctamente
    for %%A in ("dist\SistemaAutismo.exe") do echo Tamano: %%~zA bytes
    echo ✅ Construccion exitosa con script fusionado
) else (
    echo ERROR: El ejecutable no fue creado
    echo Verifique los errores mostrados en build_exe_final.py
    pause
    exit /b 1
)

echo.
echo ==========================================
echo EJECUTABLE CREADO EXITOSAMENTE
echo ==========================================
echo Ubicacion: dist\SistemaAutismo.exe
echo Script usado: build_exe_final.py (FUSIONADO)
echo.
echo CORRECCIONES DE CONEXION INCLUIDAS:
echo - Inicializacion correcta del atributo 'data'
echo - Deteccion automatica de puerto en JavaScript
echo - Manejo de errores robusto con hasattr()
echo - Endpoint /test-connection para verificacion
echo - Sistema de respaldo BasicClusteringSystem
echo - Health check mejorado con diagnosticos
echo - Verificacion automatica de dependencias
echo - Manejo robusto de codificacion UTF-8
echo.
echo Caracteristicas:
echo - Aplicacion independiente (no requiere Python)
echo - Servidor Flask integrado
echo - Abre navegador automaticamente
echo - Clustering interactivo DBSCAN + GMM + ML
echo - Analisis para edades 1-16 anos
echo - SIN ERRORES EN BUCLE DE CONEXION
echo - DETECCION AUTOMATICA DE PUERTO
echo - CONSTRUCCION ROBUSTA SIN ERRORES
echo.
echo El ejecutable esta listo para distribuir!
echo TODAS LAS CORRECCIONES APLICADAS EN UN SOLO SCRIPT
echo NO DEBERIA MOSTRAR "Sin conexion - Modo local"
echo.
pause
