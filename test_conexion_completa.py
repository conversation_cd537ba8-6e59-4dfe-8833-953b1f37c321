#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de diagnóstico completo para verificar la conexión entre frontend y backend
"""

import sys
import os
import time
import socket
import threading
import webbrowser
from pathlib import Path

def test_port_availability():
    """Prueba si los puertos comunes están disponibles"""
    print("🔍 VERIFICANDO PUERTOS DISPONIBLES")
    print("=" * 40)
    
    common_ports = [5000, 5001, 5002, 5003, 5004, 5005]
    available_ports = []
    
    for port in common_ports:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        result = sock.connect_ex(('127.0.0.1', port))
        sock.close()
        
        if result != 0:  # Puerto disponible
            available_ports.append(port)
            print(f"✅ Puerto {port}: Disponible")
        else:  # Puerto ocupado
            print(f"❌ Puerto {port}: Ocupado")
    
    print(f"\n📊 Puertos disponibles: {available_ports}")
    return available_ports

def test_flask_server():
    """Prueba si el servidor Flask está funcionando con timeout"""
    print("\n🔍 VERIFICANDO SERVIDOR FLASK")
    print("=" * 40)

    import signal
    import threading

    def timeout_handler(signum, frame):
        raise TimeoutError("Timeout en verificación de Flask")

    def test_with_timeout():
        try:
            # Importar y verificar que app.py funciona
            print("📦 Importando app.py...")

            # Usar threading para evitar bloqueos
            import_result = {'success': False, 'error': None, 'app': None, 'clustering_system': None}

            def import_app():
                try:
                    from app import app, clustering_system
                    import_result['success'] = True
                    import_result['app'] = app
                    import_result['clustering_system'] = clustering_system
                except Exception as e:
                    import_result['error'] = str(e)

            import_thread = threading.Thread(target=import_app)
            import_thread.daemon = True
            import_thread.start()
            import_thread.join(timeout=10)  # 10 segundos timeout

            if not import_result['success']:
                if import_result['error']:
                    print(f"❌ Error importando app.py: {import_result['error']}")
                else:
                    print("❌ Timeout importando app.py")
                return False

            print("✅ app.py importado correctamente")
            app = import_result['app']
            clustering_system = import_result['clustering_system']

            # Verificar sistema de clustering de forma segura
            print("🤖 Verificando sistema de clustering...")
            try:
                if clustering_system is not None:
                    if hasattr(clustering_system, 'data'):
                        if clustering_system.data is not None:
                            print(f"✅ Datos cargados: {len(clustering_system.data)} casos")
                        else:
                            print("⚠️ Sistema inicializado pero sin datos")
                    else:
                        print("❌ Sistema de clustering no tiene atributo 'data'")
                else:
                    print("❌ clustering_system es None")
            except Exception as e:
                print(f"⚠️ Error verificando clustering_system: {e}")

            # Verificar endpoints de forma segura
            print("🌐 Verificando endpoints...")
            try:
                with app.test_client() as client:
                    # Test health endpoint
                    try:
                        response = client.get('/health')
                        if response.status_code == 200:
                            print("✅ /health endpoint funcionando")
                            try:
                                data = response.get_json()
                                print(f"   Status: {data.get('status')}")
                                print(f"   Data loaded: {data.get('data_loaded')}")
                                print(f"   Models trained: {data.get('models_trained')}")
                            except:
                                print("   (Error parseando respuesta JSON)")
                        else:
                            print(f"❌ /health endpoint error: {response.status_code}")
                    except Exception as e:
                        print(f"❌ Error en /health: {e}")

                    # Test connection endpoint
                    try:
                        response = client.get('/test-connection')
                        if response.status_code == 200:
                            print("✅ /test-connection endpoint funcionando")
                        else:
                            print(f"❌ /test-connection endpoint error: {response.status_code}")
                    except Exception as e:
                        print(f"❌ Error en /test-connection: {e}")
            except Exception as e:
                print(f"❌ Error creando test client: {e}")

            return True

        except Exception as e:
            print(f"❌ Error general en servidor Flask: {e}")
            return False

    try:
        # Configurar timeout de 30 segundos
        if hasattr(signal, 'SIGALRM'):  # Unix/Linux
            signal.signal(signal.SIGALRM, timeout_handler)
            signal.alarm(30)

        result = test_with_timeout()

        if hasattr(signal, 'SIGALRM'):
            signal.alarm(0)  # Cancelar timeout

        return result

    except TimeoutError:
        print("❌ Timeout verificando servidor Flask (30s)")
        return False
    except Exception as e:
        print(f"❌ Error inesperado: {e}")
        return False

def test_frontend_files():
    """Verifica que los archivos del frontend existan"""
    print("\n🔍 VERIFICANDO ARCHIVOS FRONTEND")
    print("=" * 40)
    
    required_files = [
        'index.html',
        'styles.css', 
        'script.js',
        'clustering_visualizer.py',
        'datos.csv'
    ]
    
    missing_files = []
    
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}: Encontrado")
        else:
            print(f"❌ {file}: Faltante")
            missing_files.append(file)
    
    if missing_files:
        print(f"\n⚠️ Archivos faltantes: {missing_files}")
        return False
    else:
        print("\n✅ Todos los archivos frontend encontrados")
        return True

def test_javascript_connection():
    """Verifica la configuración de conexión en JavaScript"""
    print("\n🔍 VERIFICANDO CONFIGURACIÓN JAVASCRIPT")
    print("=" * 40)
    
    try:
        with open('script.js', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Buscar configuración de API_BASE_URL
        if 'API_BASE_URL' in content:
            print("✅ Variable API_BASE_URL encontrada")
            
            # Buscar función de detección de puerto
            if 'detectServerPort' in content:
                print("✅ Función detectServerPort encontrada")
            else:
                print("❌ Función detectServerPort no encontrada")
            
            # Buscar función de verificación de conexión
            if 'checkBackendConnection' in content:
                print("✅ Función checkBackendConnection encontrada")
            else:
                print("❌ Función checkBackendConnection no encontrada")
            
            return True
        else:
            print("❌ Variable API_BASE_URL no encontrada")
            return False
            
    except Exception as e:
        print(f"❌ Error leyendo script.js: {e}")
        return False

def start_test_server():
    """Inicia un servidor de prueba"""
    print("\n🚀 INICIANDO SERVIDOR DE PRUEBA")
    print("=" * 40)
    
    try:
        from app import app
        
        # Encontrar puerto disponible
        available_ports = test_port_availability()
        if not available_ports:
            print("❌ No hay puertos disponibles")
            return None
        
        port = available_ports[0]
        print(f"🌐 Iniciando servidor en puerto {port}...")
        
        # Iniciar servidor en hilo separado
        def run_server():
            app.run(host='127.0.0.1', port=port, debug=False, use_reloader=False)
        
        server_thread = threading.Thread(target=run_server, daemon=True)
        server_thread.start()
        
        # Esperar a que el servidor esté listo
        print("⏳ Esperando servidor...")
        for i in range(10):
            try:
                import urllib.request
                url = f"http://127.0.0.1:{port}/health"
                request = urllib.request.Request(url)
                with urllib.request.urlopen(request, timeout=2) as response:
                    if response.getcode() == 200:
                        print(f"✅ Servidor listo en puerto {port}")
                        return port
            except:
                time.sleep(1)
        
        print("❌ Servidor no respondió a tiempo")
        return None
        
    except Exception as e:
        print(f"❌ Error iniciando servidor: {e}")
        return None

def test_browser_connection(port):
    """Prueba la conexión desde el navegador"""
    print(f"\n🌐 PROBANDO CONEXIÓN DEL NAVEGADOR")
    print("=" * 40)
    
    url = f"http://127.0.0.1:{port}"
    print(f"🔗 URL de prueba: {url}")
    
    try:
        # Abrir navegador
        webbrowser.open(url, new=2)
        print("✅ Navegador abierto")
        
        print("\n📋 INSTRUCCIONES PARA VERIFICAR:")
        print("1. Verifique que la página se carga correctamente")
        print("2. Abra las herramientas de desarrollador (F12)")
        print("3. Vaya a la pestaña 'Console'")
        print("4. Busque mensajes de conexión:")
        print("   ✅ '✅ Servidor encontrado en puerto XXXX'")
        print("   ✅ '✅ Conexión establecida con backend Python completo'")
        print("5. En la esquina superior derecha debe decir:")
        print("   ✅ 'Conectado - Puerto XXXX - Clustering K=2/K=3 Disponible'")
        
        return True
        
    except Exception as e:
        print(f"❌ Error abriendo navegador: {e}")
        return False

def main():
    """Función principal de diagnóstico"""
    print("🔧 DIAGNÓSTICO COMPLETO DE CONEXIÓN")
    print("🧠 Sistema Híbrido de Evaluación de Autismo")
    print("=" * 60)
    
    tests = [
        ("Archivos Frontend", test_frontend_files),
        ("Configuración JavaScript", test_javascript_connection),
        ("Servidor Flask", test_flask_server),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        if test_func():
            passed += 1
            print(f"✅ {test_name}: PASÓ")
        else:
            print(f"❌ {test_name}: FALLÓ")
    
    print("\n" + "=" * 60)
    print("📊 RESUMEN DE DIAGNÓSTICO")
    print("=" * 60)
    print(f"✅ Pruebas pasadas: {passed}/{total}")
    
    if passed == total:
        print("🎉 TODAS LAS PRUEBAS BÁSICAS PASARON")
        print("\n🚀 Iniciando servidor de prueba...")
        
        port = start_test_server()
        if port:
            print(f"\n✅ SERVIDOR DE PRUEBA ACTIVO EN PUERTO {port}")
            test_browser_connection(port)
            
            print("\n🔄 Servidor ejecutándose...")
            print("🛑 Presione Ctrl+C para detener")
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                print("\n🛑 Deteniendo servidor de prueba...")
        else:
            print("❌ No se pudo iniciar servidor de prueba")
    else:
        print("⚠️ ALGUNAS PRUEBAS FALLARON")
        print("🔧 Revise los errores mostrados arriba")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
