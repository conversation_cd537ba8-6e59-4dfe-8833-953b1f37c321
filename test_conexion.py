#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de diagnóstico para probar la conexión entre el frontend y backend
"""

import requests
import time
import json
import sys

def test_server_connection(port=5000):
    """Prueba la conexión básica al servidor"""
    print(f"🔍 Probando conexión al servidor en puerto {port}...")
    
    base_url = f"http://127.0.0.1:{port}"
    
    tests = [
        ("Health Check", f"{base_url}/health"),
        ("Test Connection", f"{base_url}/test-connection"),
        ("Index HTML", f"{base_url}/"),
    ]
    
    results = {}
    
    for test_name, url in tests:
        print(f"\n📡 {test_name}: {url}")
        try:
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                print(f"✅ {test_name}: OK ({response.status_code})")
                print(f"   Content-Type: {response.headers.get('content-type', 'N/A')}")
                
                if 'application/json' in response.headers.get('content-type', ''):
                    try:
                        data = response.json()
                        print(f"   Status: {data.get('status', 'N/A')}")
                        print(f"   Message: {data.get('message', 'N/A')}")
                    except:
                        print("   JSON válido pero no parseado")
                else:
                    print(f"   Content Length: {len(response.text)} chars")
                
                results[test_name] = True
            else:
                print(f"❌ {test_name}: Error {response.status_code}")
                results[test_name] = False
                
        except requests.exceptions.ConnectionError:
            print(f"❌ {test_name}: No se puede conectar al servidor")
            results[test_name] = False
        except requests.exceptions.Timeout:
            print(f"❌ {test_name}: Timeout")
            results[test_name] = False
        except Exception as e:
            print(f"❌ {test_name}: Error - {e}")
            results[test_name] = False
    
    return results

def test_predict_endpoint(port=5000):
    """Prueba el endpoint de predicción"""
    print(f"\n🎯 Probando endpoint de predicción...")
    
    url = f"http://127.0.0.1:{port}/predict"
    
    # Datos de prueba
    test_data = {
        'A1': 1, 'A2': 1, 'A3': 1, 'A4': 0, 'A5': 1,
        'A6': 1, 'A7': 1, 'A8': 1, 'A9': 0, 'A10': 1,
        'Age_Mons': 36,
        'selected_k': 3
    }
    
    try:
        print(f"📤 Enviando datos: {test_data}")
        response = requests.post(
            url, 
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if response.status_code == 200:
            print("✅ Predicción: OK")
            data = response.json()
            print(f"   Risk Score: {data.get('corrected_risk_score', 'N/A')}")
            print(f"   Alert Level: {data.get('alert_level', 'N/A')}")
            print(f"   Age Group: {data.get('age_group', 'N/A')}")
            return True
        else:
            print(f"❌ Predicción: Error {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error en predicción: {e}")
        return False

def scan_ports():
    """Escanea puertos comunes para encontrar el servidor"""
    print("\n🔍 Escaneando puertos comunes...")
    
    common_ports = [5000, 5001, 8000, 8080, 3000, 3001]
    active_servers = []
    
    for port in common_ports:
        try:
            response = requests.get(f"http://127.0.0.1:{port}/health", timeout=2)
            if response.status_code == 200:
                print(f"✅ Servidor encontrado en puerto {port}")
                active_servers.append(port)
        except:
            pass
    
    if not active_servers:
        print("❌ No se encontraron servidores activos")
    
    return active_servers

def main():
    """Función principal de diagnóstico"""
    print("🔧 DIAGNÓSTICO DE CONEXIÓN - SISTEMA DE AUTISMO")
    print("=" * 50)
    
    # 1. Escanear puertos
    active_ports = scan_ports()
    
    if not active_ports:
        print("\n❌ No se encontró ningún servidor activo")
        print("🔧 Asegúrese de que el ejecutable esté funcionando")
        input("Presione Enter para salir...")
        return
    
    # 2. Probar cada servidor encontrado
    for port in active_ports:
        print(f"\n{'='*20} PUERTO {port} {'='*20}")
        
        # Probar conexiones básicas
        connection_results = test_server_connection(port)
        
        # Probar endpoint de predicción
        predict_result = test_predict_endpoint(port)
        
        # Resumen
        total_tests = len(connection_results) + 1
        passed_tests = sum(connection_results.values()) + (1 if predict_result else 0)
        
        print(f"\n📊 RESUMEN PUERTO {port}:")
        print(f"✅ Pruebas pasadas: {passed_tests}/{total_tests}")
        
        if passed_tests == total_tests:
            print(f"🎉 PUERTO {port}: COMPLETAMENTE FUNCIONAL")
        elif passed_tests > 0:
            print(f"⚠️ PUERTO {port}: FUNCIONAMIENTO PARCIAL")
        else:
            print(f"❌ PUERTO {port}: NO FUNCIONAL")
    
    print("\n" + "=" * 50)
    print("🔧 DIAGNÓSTICO COMPLETADO")
    print("=" * 50)
    
    if any(sum(test_server_connection(port).values()) > 0 for port in active_ports):
        print("✅ Al menos un servidor está respondiendo")
        print("🌐 Puede acceder manualmente a:")
        for port in active_ports:
            print(f"   http://127.0.0.1:{port}")
    else:
        print("❌ Ningún servidor está funcionando correctamente")
        print("🔧 Posibles soluciones:")
        print("   • Reiniciar el ejecutable")
        print("   • Verificar que no haya antivirus bloqueando")
        print("   • Ejecutar como administrador")
    
    input("\nPresione Enter para salir...")

if __name__ == "__main__":
    main()
