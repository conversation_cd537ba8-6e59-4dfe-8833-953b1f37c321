#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de prueba para verificar que todos los componentes funcionen antes de crear el ejecutable
"""

import os
import sys
import importlib
from pathlib import Path

def test_imports():
    """Prueba que todas las dependencias se puedan importar"""
    print("🔍 Probando imports de dependencias...")
    
    required_modules = [
        'flask',
        'flask_cors',
        'pandas',
        'numpy',
        'sklearn',
        'matplotlib',
        'seaborn',
        'threading',
        'webbrowser',
        'socket'
    ]
    
    failed_imports = []
    
    for module in required_modules:
        try:
            importlib.import_module(module)
            print(f"✅ {module}")
        except ImportError as e:
            print(f"❌ {module}: {e}")
            failed_imports.append(module)
    
    return len(failed_imports) == 0

def test_files():
    """Verifica que todos los archivos necesarios estén presentes"""
    print("\n📁 Verificando archivos necesarios...")
    
    required_files = [
        'main.py',
        'app.py',
        'clustering_visualizer.py',
        'datos.csv',
        'index.html',
        'styles.css',
        'script.js',
        'clustering_ui.js'
    ]
    
    missing_files = []
    
    for file in required_files:
        if os.path.exists(file):
            size = os.path.getsize(file)
            print(f"✅ {file} ({size} bytes)")
        else:
            print(f"❌ {file} - NO ENCONTRADO")
            missing_files.append(file)
    
    # Verificar carpeta PaginaGraficos
    if os.path.exists('PaginaGraficos'):
        print("✅ PaginaGraficos/ (carpeta)")
    else:
        print("❌ PaginaGraficos/ - NO ENCONTRADA")
        missing_files.append('PaginaGraficos/')
    
    return len(missing_files) == 0

def test_sequence():
    """Prueba la secuencia correcta: clustering_visualizer -> app -> index.html"""
    print("\n🔄 Probando secuencia de ejecución correcta...")

    try:
        # 1. Primero clustering_visualizer
        print("   [1/3] Importando clustering_visualizer...")
        import clustering_visualizer
        print("   ✅ clustering_visualizer importado")

        # 2. Luego app (que depende de clustering_visualizer)
        print("   [2/3] Importando aplicación Flask...")
        from app import app, clustering_system
        print("   ✅ Aplicación Flask importada")
        print(f"   ✅ Sistema de clustering: {type(clustering_system)}")

        # 3. Verificar que index.html sea accesible
        print("   [3/3] Verificando acceso a index.html...")
        if os.path.exists('index.html'):
            print("   ✅ index.html encontrado")
        else:
            print("   ❌ index.html no encontrado")
            return False

        return True
    except Exception as e:
        print(f"   ❌ Error en secuencia: {e}")
        return False

def test_main_script():
    """Prueba que el script principal se pueda importar"""
    print("\n🚀 Probando script principal...")
    
    try:
        import main
        print("✅ Script principal importado correctamente")
        print(f"✅ Clase AutismAssessmentApp disponible: {hasattr(main, 'AutismAssessmentApp')}")
        return True
    except Exception as e:
        print(f"❌ Error importando script principal: {e}")
        return False

def test_data_loading():
    """Prueba que los datos se puedan cargar"""
    print("\n📊 Probando carga de datos...")
    
    try:
        from app import clustering_system
        data = clustering_system.load_data()
        if data is not None:
            print(f"✅ Datos cargados: {len(data)} filas, {len(data.columns)} columnas")
            return True
        else:
            print("❌ Error: No se pudieron cargar los datos")
            return False
    except Exception as e:
        print(f"❌ Error cargando datos: {e}")
        return False

def test_clustering():
    """Prueba básica del sistema de clustering"""
    print("\n🎯 Probando sistema de clustering...")
    
    try:
        from app import analyze_user_responses
        
        # Datos de prueba
        test_responses = {
            'A1': 1, 'A2': 1, 'A3': 1, 'A4': 0, 'A5': 1,
            'A6': 1, 'A7': 1, 'A8': 1, 'A9': 0, 'A10': 1,
            'Age_Mons': 36
        }
        
        result = analyze_user_responses(test_responses, selected_k=3)
        
        if result and result.get('success'):
            print("✅ Sistema de clustering funcionando")
            print(f"✅ Resultado obtenido: {result.get('selected_algorithm', 'N/A')}")
            return True
        else:
            print(f"❌ Error en clustering: {result.get('error', 'Desconocido') if result else 'Sin resultado'}")
            return False
    except Exception as e:
        print(f"❌ Error probando clustering: {e}")
        return False

def main():
    """Función principal de pruebas"""
    print("🧠 PRUEBAS DEL SISTEMA DE EVALUACIÓN DE AUTISMO")
    print("=" * 50)
    print("🔧 Verificando que todo esté listo para crear el ejecutable")
    print("=" * 50)
    
    tests = [
        ("Dependencias", test_imports),
        ("Archivos", test_files),
        ("Secuencia de Ejecución", test_sequence),
        ("Script Principal", test_main_script),
        ("Carga de Datos", test_data_loading),
        ("Sistema de Clustering", test_clustering)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: PASÓ")
            else:
                print(f"❌ {test_name}: FALLÓ")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
    
    print("\n" + "=" * 50)
    print("📊 RESUMEN DE PRUEBAS")
    print("=" * 50)
    print(f"✅ Pruebas pasadas: {passed}/{total}")
    print(f"❌ Pruebas fallidas: {total - passed}/{total}")
    
    if passed == total:
        print("\n🎉 TODAS LAS PRUEBAS PASARON")
        print("✅ El sistema está listo para crear el ejecutable")
        print("🚀 Ejecute: python build_exe.py")
    else:
        print("\n⚠️ ALGUNAS PRUEBAS FALLARON")
        print("🔧 Corrija los errores antes de crear el ejecutable")
        print("📋 Revise las dependencias y archivos faltantes")
    
    print("=" * 50)
    input("Presione Enter para continuar...")

if __name__ == "__main__":
    main()
